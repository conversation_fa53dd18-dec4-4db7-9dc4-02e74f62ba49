/**
 * Service Access Dashboard Component
 * Displays API access logs, service statistics, and security monitoring
 * Integrates with the new FileRoutingService API Server
 */

import React, { useState, useEffect } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { apiServerClient } from '../services/apiServerClient'
import {
  faChartLine,
  faExclamationTriangle,
  faCheckCircle,
  faTimesCircle,
  faEye,
  faDownload,
  faRefresh,
  faFilter,
  faSearch
} from '@fortawesome/free-solid-svg-icons'

interface ServiceStats {
  serviceId: string
  serviceName: string
  version: string
  status: 'active' | 'suspended' | 'revoked'
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  successRate: number
  lastAccess?: string
  createdAt: string
  capabilities: string[]
  developer: {
    name: string
    email: string
    organization?: string
  }
}

interface AccessLogEntry {
  id: string
  serviceId: string
  endpoint: string
  method: string
  success: boolean
  ip: string
  timestamp: string
  responseTime?: number
  errorCode?: string
}

interface SecurityEvent {
  id: string
  type: 'FAILED_AUTH' | 'RATE_LIMIT' | 'SUSPICIOUS_ACTIVITY' | 'TOKEN_EXPIRED'
  serviceId?: string
  ip: string
  timestamp: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
  details: any
}

export const ServiceAccessDashboard: React.FC = () => {
  const [services, setServices] = useState<ServiceStats[]>([])
  const [accessLogs, setAccessLogs] = useState<AccessLogEntry[]>([])
  const [securityEvents, setSecurityEvents] = useState<SecurityEvent[]>([])
  const [loading, setLoading] = useState(true)
  const [selectedService, setSelectedService] = useState<string | null>(null)
  const [activeTab, setActiveTab] = useState<'overview' | 'logs' | 'security'>('overview')
  const [searchTerm, setSearchTerm] = useState('')
  const [timeRange, setTimeRange] = useState('24h')

  useEffect(() => {
    loadDashboardData()
    const interval = setInterval(loadDashboardData, 30000) // Refresh every 30 seconds
    return () => clearInterval(interval)
  }, [])

  const loadDashboardData = async () => {
    try {
      // Initialize API client if needed
      await apiServerClient.initialize()

      // Load data from API server (with fallback to mock data for demo)
      try {
        const [servicesData, logsData, securityData] = await Promise.all([
          apiServerClient.getRegisteredServices(),
          apiServerClient.getRecentLogs(50),
          apiServerClient.getSecurityEvents(20)
        ])

        // Convert API data to component format
        const convertedServices: ServiceStats[] = servicesData.map(service => ({
          serviceId: service.serviceId,
          serviceName: service.name,
          version: service.version,
          status: service.status,
          totalRequests: service.totalRequests,
          successfulRequests: service.successfulRequests,
          failedRequests: service.failedRequests,
          successRate: service.totalRequests > 0 ? (service.successfulRequests / service.totalRequests) * 100 : 0,
          lastAccess: service.lastAccess,
          createdAt: service.createdAt,
          capabilities: service.capabilities,
          developer: service.developerInfo
        }))

        setServices(convertedServices)
        setAccessLogs(logsData)
        setSecurityEvents(securityData)
        return
      } catch (apiError) {
        console.warn('API server not available, using mock data:', apiError)
      }

      // Fallback to mock data if API server is not available
      
      const mockServices: ServiceStats[] = [
        {
          serviceId: 'srv_1234567890',
          serviceName: 'FileProcessor Pro',
          version: '1.2.0',
          status: 'active',
          totalRequests: 1247,
          successfulRequests: 1198,
          failedRequests: 49,
          successRate: 96.1,
          lastAccess: new Date(Date.now() - 300000).toISOString(), // 5 minutes ago
          createdAt: new Date(Date.now() - 86400000 * 7).toISOString(), // 7 days ago
          capabilities: ['file.upload', 'file.read', 'file.process'],
          developer: {
            name: 'John Doe',
            email: '<EMAIL>',
            organization: 'TechCorp'
          }
        },
        {
          serviceId: 'srv_2345678901',
          serviceName: 'BackupService',
          version: '3.0.0',
          status: 'active',
          totalRequests: 2103,
          successfulRequests: 2051,
          failedRequests: 52,
          successRate: 97.5,
          lastAccess: new Date(Date.now() - 600000).toISOString(), // 10 minutes ago
          createdAt: new Date(Date.now() - 86400000 * 14).toISOString(), // 14 days ago
          capabilities: ['file.upload', 'file.read', 'file.delete'],
          developer: {
            name: 'Jane Smith',
            email: '<EMAIL>',
            organization: 'CloudSync Corp'
          }
        },
        {
          serviceId: 'srv_3456789012',
          serviceName: 'TestPlugin',
          version: '0.1.0',
          status: 'suspended',
          totalRequests: 45,
          successfulRequests: 33,
          failedRequests: 12,
          successRate: 73.3,
          lastAccess: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
          createdAt: new Date(Date.now() - 86400000 * 3).toISOString(), // 3 days ago
          capabilities: ['file.upload'],
          developer: {
            name: 'Dev Studio',
            email: '<EMAIL>'
          }
        }
      ]

      const mockLogs: AccessLogEntry[] = [
        {
          id: 'log_1',
          serviceId: 'srv_1234567890',
          endpoint: '/api/v1/files/upload',
          method: 'POST',
          success: true,
          ip: '*************',
          timestamp: new Date(Date.now() - 60000).toISOString(),
          responseTime: 245
        },
        {
          id: 'log_2',
          serviceId: 'srv_2345678901',
          endpoint: '/api/v1/files/list',
          method: 'GET',
          success: true,
          ip: '*************',
          timestamp: new Date(Date.now() - 120000).toISOString(),
          responseTime: 89
        },
        {
          id: 'log_3',
          serviceId: 'srv_3456789012',
          endpoint: '/api/v1/files/upload',
          method: 'POST',
          success: false,
          ip: '*************',
          timestamp: new Date(Date.now() - 180000).toISOString(),
          responseTime: 1200,
          errorCode: 'INVALID_TOKEN'
        }
      ]

      const mockSecurityEvents: SecurityEvent[] = [
        {
          id: 'sec_1',
          type: 'FAILED_AUTH',
          serviceId: 'srv_3456789012',
          ip: '*************',
          timestamp: new Date(Date.now() - 300000).toISOString(),
          severity: 'MEDIUM',
          details: { reason: 'Invalid API key', attempts: 3 }
        },
        {
          id: 'sec_2',
          type: 'RATE_LIMIT',
          serviceId: 'srv_1234567890',
          ip: '*************',
          timestamp: new Date(Date.now() - 600000).toISOString(),
          severity: 'LOW',
          details: { requestCount: 105, windowMs: 900000 }
        }
      ]

      setServices(mockServices)
      setAccessLogs(mockLogs)
      setSecurityEvents(mockSecurityEvents)
    } catch (error) {
      console.error('Failed to load dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'text-green-600'
      case 'suspended': return 'text-yellow-600'
      case 'revoked': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active': return faCheckCircle
      case 'suspended': return faExclamationTriangle
      case 'revoked': return faTimesCircle
      default: return faTimesCircle
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'LOW': return 'text-blue-600'
      case 'MEDIUM': return 'text-yellow-600'
      case 'HIGH': return 'text-orange-600'
      case 'CRITICAL': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const formatTimestamp = (timestamp: string) => {
    return new Date(timestamp).toLocaleString()
  }

  const filteredServices = services.filter(service =>
    service.serviceName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    service.developer.name.toLowerCase().includes(searchTerm.toLowerCase())
  )

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <FontAwesomeIcon icon={faRefresh} className="animate-spin text-2xl text-blue-600" />
        <span className="ml-2 text-gray-600">Loading dashboard data...</span>
      </div>
    )
  }

  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Service Access Dashboard</h1>
        <p className="text-gray-600 mt-2">
          Monitor API access, service performance, and security events in real-time.
        </p>
      </div>

      {/* Summary Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faChartLine} className="text-blue-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Total Services</h3>
              <p className="text-2xl font-bold text-blue-600">{services.length}</p>
              <p className="text-sm text-gray-500">
                {services.filter(s => s.status === 'active').length} active
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faCheckCircle} className="text-green-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">API Calls</h3>
              <p className="text-2xl font-bold text-green-600">
                {services.reduce((sum, s) => sum + s.totalRequests, 0).toLocaleString()}
              </p>
              <p className="text-sm text-gray-500">Last 24 hours</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faExclamationTriangle} className="text-yellow-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Success Rate</h3>
              <p className="text-2xl font-bold text-yellow-600">
                {(services.reduce((sum, s) => sum + s.successRate, 0) / services.length).toFixed(1)}%
              </p>
              <p className="text-sm text-gray-500">Average</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow border">
          <div className="flex items-center">
            <FontAwesomeIcon icon={faTimesCircle} className="text-red-600 text-2xl" />
            <div className="ml-4">
              <h3 className="text-lg font-semibold text-gray-900">Security Events</h3>
              <p className="text-2xl font-bold text-red-600">{securityEvents.length}</p>
              <p className="text-sm text-gray-500">Last 24 hours</p>
            </div>
          </div>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="mb-6">
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            {[
              { id: 'overview', label: 'Service Overview' },
              { id: 'logs', label: 'Access Logs' },
              { id: 'security', label: 'Security Events' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id as any)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </div>

      {/* Search and Filters */}
      <div className="mb-6 flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <input
              type="text"
              placeholder="Search services..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
          
          <select
            value={timeRange}
            onChange={(e) => setTimeRange(e.target.value)}
            className="px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="1h">Last Hour</option>
            <option value="24h">Last 24 Hours</option>
            <option value="7d">Last 7 Days</option>
            <option value="30d">Last 30 Days</option>
          </select>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={loadDashboardData}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
          >
            <FontAwesomeIcon icon={faRefresh} className="mr-2" />
            Refresh
          </button>
          
          <button className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors">
            <FontAwesomeIcon icon={faDownload} className="mr-2" />
            Export
          </button>
        </div>
      </div>

      {/* Tab Content */}
      {activeTab === 'overview' && (
        <div className="bg-white rounded-lg shadow border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Registered Services</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Requests</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Success Rate</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Last Access</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredServices.map((service) => (
                  <tr key={service.serviceId} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div>
                        <div className="text-sm font-medium text-gray-900">{service.serviceName}</div>
                        <div className="text-sm text-gray-500">v{service.version} • {service.developer.name}</div>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(service.status)}`}>
                        <FontAwesomeIcon icon={getStatusIcon(service.status)} className="mr-1" />
                        {service.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {service.totalRequests.toLocaleString()}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {service.successRate.toFixed(1)}%
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {service.lastAccess ? formatTimestamp(service.lastAccess) : 'Never'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                      <button
                        onClick={() => setSelectedService(service.serviceId)}
                        className="text-blue-600 hover:text-blue-900 mr-3"
                      >
                        <FontAwesomeIcon icon={faEye} />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'logs' && (
        <div className="bg-white rounded-lg shadow border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Recent Access Logs</h3>
          </div>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Timestamp</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Service</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Endpoint</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Response Time</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">IP Address</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {accessLogs.map((log) => (
                  <tr key={log.id} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {formatTimestamp(log.timestamp)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {services.find(s => s.serviceId === log.serviceId)?.serviceName || log.serviceId}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      <span className="font-mono">{log.method}</span> {log.endpoint}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                        log.success ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                      }`}>
                        {log.success ? 'Success' : log.errorCode || 'Failed'}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                      {log.responseTime ? `${log.responseTime}ms` : '-'}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500 font-mono">
                      {log.ip}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {activeTab === 'security' && (
        <div className="bg-white rounded-lg shadow border">
          <div className="px-6 py-4 border-b border-gray-200">
            <h3 className="text-lg font-semibold text-gray-900">Security Events</h3>
          </div>
          <div className="space-y-4 p-6">
            {securityEvents.map((event) => (
              <div key={event.id} className="border border-gray-200 rounded-lg p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getSeverityColor(event.severity)}`}>
                      {event.severity}
                    </span>
                    <span className="ml-3 font-medium text-gray-900">{event.type}</span>
                  </div>
                  <span className="text-sm text-gray-500">{formatTimestamp(event.timestamp)}</span>
                </div>
                <div className="mt-2 text-sm text-gray-600">
                  <p>IP: <span className="font-mono">{event.ip}</span></p>
                  {event.serviceId && (
                    <p>Service: {services.find(s => s.serviceId === event.serviceId)?.serviceName || event.serviceId}</p>
                  )}
                  <p>Details: {JSON.stringify(event.details)}</p>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
