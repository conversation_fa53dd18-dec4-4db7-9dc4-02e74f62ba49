/**
 * HttpFileRoutingService
 * HTTP-based file upload service with IPC fallback
 * Implements the hybrid approach for reliable file uploads
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { fileRoutingService } from './fileRoutingService'
import {
  FileDestination,
  FileUploadResponse,
  UploadedFile,
  FileError
} from '../types/fileRouting'

export interface HttpUploadOptions {
  timeout?: number
  retryAttempts?: number
  fallbackToIpc?: boolean
}

class HttpFileRoutingService extends BaseService {
  private baseUrl = 'http://localhost:3001'
  private token: string | null = null
  private readonly defaultOptions: HttpUploadOptions = {
    timeout: 30000, // 30 seconds
    retryAttempts: 2,
    fallbackToIpc: true
  }

  constructor() {
    super({
      name: 'HttpFileRoutingService',
      autoInitialize: false
    })
  }

  protected async doInitialize(): Promise<void> {
    // Check if HTTP API server is available
    try {
      const response = await fetch(`${this.baseUrl}/health`, { 
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      })
      
      if (!response.ok) {
        throw new Error(`HTTP API server not healthy: ${response.status}`)
      }

      this.logger.info('HTTP API server is available', 'doInitialize')
    } catch (error) {
      this.logger.warn('HTTP API server not available, will use IPC fallback', 'doInitialize', error)
    }
  }

  /**
   * Main entry point for file uploads - tries HTTP first, falls back to IPC
   */
  async handleFileDrop(
    files: File[], 
    destination: FileDestination, 
    options: HttpUploadOptions = {}
  ): Promise<FileUploadResponse> {
    return await this.executeOperationOrThrow(
      'handleFileDrop',
      async () => {
        const finalOptions = { ...this.defaultOptions, ...options }

        try {
          // Try HTTP API first
          this.logger.debug(`Attempting HTTP upload for ${files.length} files`, 'handleFileDrop')
          return await this.uploadViaHttp(files, destination, finalOptions)
        } catch (error) {
          this.logger.warn('HTTP upload failed', 'handleFileDrop', error)
          
          if (finalOptions.fallbackToIpc) {
            // Fallback to IPC
            this.logger.debug('Falling back to IPC upload', 'handleFileDrop')
            return await this.uploadViaIpc(files, destination)
          } else {
            throw error
          }
        }
      },
      { fileCount: files.length, contextId: destination.contextId }
    )
  }

  /**
   * Upload files via HTTP API
   */
  private async uploadViaHttp(
    files: File[], 
    destination: FileDestination,
    options: HttpUploadOptions
  ): Promise<FileUploadResponse> {
    // Prepare form data
    const formData = new FormData()
    
    files.forEach(file => formData.append('files', file))
    formData.append('destination', JSON.stringify(destination))
    formData.append('options', JSON.stringify({ autoRoute: true }))

    // Get authentication token
    const token = await this.getAuthToken()

    // Create abort controller for timeout
    const controller = new AbortController()
    const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000)

    try {
      const response = await fetch(`${this.baseUrl}/api/v1/files/upload`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
        },
        body: formData,
        signal: controller.signal
      })

      clearTimeout(timeoutId)

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(`HTTP ${response.status}: ${errorData.error?.message || 'Upload failed'}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.error?.message || 'Upload failed')
      }

      // Transform response to match expected format
      return {
        success: true,
        files: result.data.files.map((file: any) => ({
          originalName: file.originalName,
          savedPath: file.savedPath,
          size: file.size,
          mimeType: file.mimeType
        })),
        errors: []
      }
    } catch (error) {
      clearTimeout(timeoutId)
      
      if (error instanceof Error && error.name === 'AbortError') {
        throw new ServiceError(
          ServiceErrorCode.TIMEOUT_ERROR,
          'HTTP upload timed out',
          { serviceName: this.serviceName, operation: 'uploadViaHttp' }
        )
      }
      
      throw error
    }
  }

  /**
   * Upload files via IPC (fallback method)
   */
  private async uploadViaIpc(files: File[], destination: FileDestination): Promise<FileUploadResponse> {
    this.logger.debug('Using IPC fallback for file upload', 'uploadViaIpc')
    return await fileRoutingService.handleFileDrop(files, destination)
  }

  /**
   * Get authentication token for HTTP API
   */
  private async getAuthToken(): Promise<string> {
    if (this.token) {
      return this.token
    }

    try {
      // For now, use a simple service registration approach
      // In production, this would be more sophisticated
      const response = await fetch(`${this.baseUrl}/api/v1/services/register`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          serviceName: 'ChatLo Frontend',
          version: '1.0.0',
          capabilities: ['file.upload', 'file.read', 'file.list'],
          developer: {
            name: 'ChatLo Team',
            email: '<EMAIL>'
          }
        })
      })

      if (!response.ok) {
        throw new Error(`Failed to register service: ${response.status}`)
      }

      const result = await response.json()
      
      if (!result.success) {
        throw new Error('Service registration failed')
      }

      // Get token
      const tokenResponse = await fetch(`${this.baseUrl}/api/v1/auth/token`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          serviceId: 'chatlo-frontend',
          permissions: ['files.upload', 'files.read', 'files.list']
        })
      })

      if (!tokenResponse.ok) {
        throw new Error(`Failed to get token: ${tokenResponse.status}`)
      }

      const tokenResult = await tokenResponse.json()
      
      if (!tokenResult.success) {
        throw new Error('Token request failed')
      }

      this.token = tokenResult.data.accessToken
      return this.token
    } catch (error) {
      this.logger.error('Failed to get auth token', 'getAuthToken', error)
      throw new ServiceError(
        ServiceErrorCode.AUTHENTICATION_ERROR,
        'Failed to authenticate with HTTP API',
        { serviceName: this.serviceName, operation: 'getAuthToken' }
      )
    }
  }

  /**
   * Check if HTTP API is available
   */
  async isHttpApiAvailable(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/health`, {
        method: 'GET',
        signal: AbortSignal.timeout(2000)
      })
      return response.ok
    } catch {
      return false
    }
  }

  /**
   * Force use of IPC method (for testing/debugging)
   */
  async uploadViaIpcOnly(files: File[], destination: FileDestination): Promise<FileUploadResponse> {
    return await this.uploadViaIpc(files, destination)
  }

  /**
   * Force use of HTTP method (for testing/debugging)
   */
  async uploadViaHttpOnly(files: File[], destination: FileDestination): Promise<FileUploadResponse> {
    return await this.uploadViaHttp(files, destination, this.defaultOptions)
  }
}

// Export singleton instance
export const httpFileRoutingService = new HttpFileRoutingService()
