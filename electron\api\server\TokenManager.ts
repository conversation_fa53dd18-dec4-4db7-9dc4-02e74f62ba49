/**
 * TokenManager
 * Handles JWT token generation, validation, and refresh
 * Implements secure token-based authentication for the API
 */

import { BaseService, ServiceError, ServiceErrorCode } from '../../../src/services/base'

export interface TokenData {
  serviceId: string
  capabilities: string[]
  expiresAt: number
  issuedAt: number
}

export interface TokenRequest {
  serviceId: string
  apiKey: string
  capabilities?: string[]
}

export interface TokenResponse {
  accessToken: string
  refreshToken: string
  expiresIn: number
  tokenType: 'Bearer'
}

export interface RefreshTokenData {
  serviceId: string
  tokenId: string
  expiresAt: number
}

class TokenManager extends BaseService {
  private readonly ACCESS_TOKEN_EXPIRY = 3600 // 1 hour
  private readonly REFRESH_TOKEN_EXPIRY = 86400 * 7 // 7 days
  private readonly TOKEN_SECRET = 'chatlo_api_secret_key' // In production, use environment variable
  
  private activeTokens: Map<string, TokenData> = new Map()
  private refreshTokens: Map<string, RefreshTokenData> = new Map()

  constructor() {
    super({
      name: 'TokenManager',
      autoInitialize: false
    })
  }

  protected async doInitialize(): Promise<void> {
    // In production, load active tokens from database
    this.logger.info('TokenManager initialized successfully', 'doInitialize')
  }

  protected async doCleanup(): Promise<void> {
    this.activeTokens.clear()
    this.refreshTokens.clear()
    this.logger.info('TokenManager cleaned up', 'doCleanup')
  }

  /**
   * Generate access and refresh tokens
   */
  async generateToken(request: TokenRequest): Promise<TokenResponse> {
    try {
      // Validate service and API key
      const isValid = await this.validateApiKey(request.serviceId, request.apiKey)
      if (!isValid) {
        throw new ServiceError(
          ServiceErrorCode.AUTHENTICATION_ERROR,
          'Invalid service ID or API key',
          { serviceName: this.serviceName, operation: 'generateToken' }
        )
      }

      const now = Date.now()
      const accessTokenId = `at_${now}_${Math.random().toString(36).substring(2, 11)}`
      const refreshTokenId = `rt_${now}_${Math.random().toString(36).substring(2, 11)}`

      // Create token data
      const tokenData: TokenData = {
        serviceId: request.serviceId,
        capabilities: request.capabilities || ['file.upload', 'file.read'],
        expiresAt: now + (this.ACCESS_TOKEN_EXPIRY * 1000),
        issuedAt: now
      }

      const refreshTokenData: RefreshTokenData = {
        serviceId: request.serviceId,
        tokenId: accessTokenId,
        expiresAt: now + (this.REFRESH_TOKEN_EXPIRY * 1000)
      }

      // Store tokens
      this.activeTokens.set(accessTokenId, tokenData)
      this.refreshTokens.set(refreshTokenId, refreshTokenData)

      // In production, these would be proper JWT tokens
      const response: TokenResponse = {
        accessToken: accessTokenId,
        refreshToken: refreshTokenId,
        expiresIn: this.ACCESS_TOKEN_EXPIRY,
        tokenType: 'Bearer'
      }

      this.logger.info('Token generated successfully', 'generateToken', {
        serviceId: request.serviceId,
        capabilities: request.capabilities,
        expiresIn: this.ACCESS_TOKEN_EXPIRY
      })

      return response
    } catch (error) {
      this.logger.error('Failed to generate token', 'generateToken', error)
      throw error
    }
  }

  /**
   * Validate an access token
   */
  async validateToken(token: string): Promise<TokenData | null> {
    try {
      const tokenData = this.activeTokens.get(token)
      
      if (!tokenData) {
        this.logger.debug('Token not found', 'validateToken', { token: token.substring(0, 10) + '...' })
        return null
      }

      // Check expiration
      if (Date.now() > tokenData.expiresAt) {
        this.activeTokens.delete(token)
        this.logger.debug('Token expired', 'validateToken', { token: token.substring(0, 10) + '...' })
        return null
      }

      return tokenData
    } catch (error) {
      this.logger.error('Failed to validate token', 'validateToken', error)
      return null
    }
  }

  /**
   * Refresh an access token
   */
  async refreshToken(refreshToken: string): Promise<TokenResponse> {
    try {
      const refreshData = this.refreshTokens.get(refreshToken)
      
      if (!refreshData) {
        throw new ServiceError(
          ServiceErrorCode.AUTHENTICATION_ERROR,
          'Invalid refresh token',
          { serviceName: this.serviceName, operation: 'refreshToken' }
        )
      }

      // Check expiration
      if (Date.now() > refreshData.expiresAt) {
        this.refreshTokens.delete(refreshToken)
        throw new ServiceError(
          ServiceErrorCode.AUTHENTICATION_ERROR,
          'Refresh token expired',
          { serviceName: this.serviceName, operation: 'refreshToken' }
        )
      }

      // Generate new tokens
      const now = Date.now()
      const newAccessTokenId = `at_${now}_${Math.random().toString(36).substring(2, 11)}`
      const newRefreshTokenId = `rt_${now}_${Math.random().toString(36).substring(2, 11)}`

      // Get original token data
      const originalToken = this.activeTokens.get(refreshData.tokenId)
      if (!originalToken) {
        throw new ServiceError(
          ServiceErrorCode.AUTHENTICATION_ERROR,
          'Original token not found',
          { serviceName: this.serviceName, operation: 'refreshToken' }
        )
      }

      // Create new token data
      const newTokenData: TokenData = {
        serviceId: refreshData.serviceId,
        capabilities: originalToken.capabilities,
        expiresAt: now + (this.ACCESS_TOKEN_EXPIRY * 1000),
        issuedAt: now
      }

      const newRefreshTokenData: RefreshTokenData = {
        serviceId: refreshData.serviceId,
        tokenId: newAccessTokenId,
        expiresAt: now + (this.REFRESH_TOKEN_EXPIRY * 1000)
      }

      // Clean up old tokens
      this.activeTokens.delete(refreshData.tokenId)
      this.refreshTokens.delete(refreshToken)

      // Store new tokens
      this.activeTokens.set(newAccessTokenId, newTokenData)
      this.refreshTokens.set(newRefreshTokenId, newRefreshTokenData)

      const response: TokenResponse = {
        accessToken: newAccessTokenId,
        refreshToken: newRefreshTokenId,
        expiresIn: this.ACCESS_TOKEN_EXPIRY,
        tokenType: 'Bearer'
      }

      this.logger.info('Token refreshed successfully', 'refreshToken', {
        serviceId: refreshData.serviceId
      })

      return response
    } catch (error) {
      this.logger.error('Failed to refresh token', 'refreshToken', error)
      throw error
    }
  }

  /**
   * Revoke a token
   */
  async revokeToken(token: string): Promise<void> {
    try {
      const tokenData = this.activeTokens.get(token)
      if (tokenData) {
        this.activeTokens.delete(token)
        
        // Also remove associated refresh tokens
        for (const [refreshToken, refreshData] of this.refreshTokens.entries()) {
          if (refreshData.tokenId === token) {
            this.refreshTokens.delete(refreshToken)
            break
          }
        }

        this.logger.info('Token revoked successfully', 'revokeToken', {
          serviceId: tokenData.serviceId
        })
      }
    } catch (error) {
      this.logger.error('Failed to revoke token', 'revokeToken', error)
    }
  }

  /**
   * Clean up expired tokens
   */
  async cleanupExpiredTokens(): Promise<void> {
    try {
      const now = Date.now()
      let cleanedCount = 0

      // Clean up access tokens
      for (const [token, data] of this.activeTokens.entries()) {
        if (now > data.expiresAt) {
          this.activeTokens.delete(token)
          cleanedCount++
        }
      }

      // Clean up refresh tokens
      for (const [token, data] of this.refreshTokens.entries()) {
        if (now > data.expiresAt) {
          this.refreshTokens.delete(token)
          cleanedCount++
        }
      }

      if (cleanedCount > 0) {
        this.logger.info('Expired tokens cleaned up', 'cleanupExpiredTokens', {
          cleanedCount
        })
      }
    } catch (error) {
      this.logger.error('Failed to cleanup expired tokens', 'cleanupExpiredTokens', error)
    }
  }

  /**
   * Validate API key (mock implementation)
   */
  private async validateApiKey(serviceId: string, apiKey: string): Promise<boolean> {
    // In production, this would validate against a database
    // For now, accept any service ID that starts with 'srv_' and API key that starts with 'ak_'
    return serviceId.startsWith('srv_') && apiKey.startsWith('ak_')
  }

  /**
   * Get token statistics
   */
  async getTokenStats(): Promise<{
    activeTokens: number
    refreshTokens: number
    expiredTokens: number
  }> {
    const now = Date.now()
    let expiredCount = 0

    for (const data of this.activeTokens.values()) {
      if (now > data.expiresAt) {
        expiredCount++
      }
    }

    return {
      activeTokens: this.activeTokens.size - expiredCount,
      refreshTokens: this.refreshTokens.size,
      expiredTokens: expiredCount
    }
  }
}

export { TokenManager }
