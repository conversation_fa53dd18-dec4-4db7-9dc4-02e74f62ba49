/**
 * Enhanced2StepFileUploadExample
 * Demonstrates the 2-step cross-platform file upload system
 * 
 * This example shows how the new system works:
 * 1. Files are dropped → Secure path extraction using native dialogs
 * 2. Paths stored temporarily → Cross-platform translation to vault structure
 * 3. File operations executed → Direct file copy without IPC content transfer
 */

import React, { useState } from 'react'
import Enhanced2StepDropZone from '../components/Enhanced2StepDropZone'
import { FileDestination, FileUploadResponse } from '../types/fileRouting'

export const Enhanced2StepFileUploadExample: React.FC = () => {
  const [uploadResults, setUploadResults] = useState<FileUploadResponse[]>([])
  const [selectedContext, setSelectedContext] = useState<string>('')
  const [uploadProgress, setUploadProgress] = useState<number>(0)
  const [isUploading, setIsUploading] = useState<boolean>(false)

  // Example destinations for different use cases
  const destinations = {
    // Context-specific upload with auto-routing
    contextAutoRoute: {
      contextId: selectedContext,
      autoRoute: true
    } as FileDestination,

    // Context-specific upload to documents folder
    contextDocuments: {
      contextId: selectedContext,
      subPath: 'documents',
      autoRoute: false
    } as FileDestination,

    // Context-specific upload to images folder
    contextImages: {
      contextId: selectedContext,
      subPath: 'images',
      autoRoute: false
    } as FileDestination,

    // Shared dropbox upload
    sharedDropbox: {
      autoRoute: true
    } as FileDestination,

    // Explicit path upload
    explicitPath: {
      path: '/custom/upload/path',
      autoRoute: false
    } as FileDestination
  }

  const handleUploadComplete = (response: FileUploadResponse) => {
    console.log('✅ Upload completed:', response)
    setUploadResults(prev => [...prev, response])
    setIsUploading(false)
    setUploadProgress(100)

    // Show success notification
    if (response.success) {
      console.log(`Successfully uploaded ${response.files.length} files`)
      response.files.forEach(file => {
        console.log(`📁 ${file.originalName} → ${file.savedPath}`)
        if (file.autoRouteFolder) {
          console.log(`🎯 Auto-routed to: ${file.autoRouteFolder}`)
        }
      })
    }
  }

  const handleUploadError = (error: string) => {
    console.error('❌ Upload error:', error)
    setIsUploading(false)
    setUploadProgress(0)
  }

  const handleUploadProgress = (progress: number) => {
    setUploadProgress(progress)
    setIsUploading(progress > 0 && progress < 100)
  }

  const clearResults = () => {
    setUploadResults([])
    setUploadProgress(0)
  }

  return (
    <div className="max-w-4xl mx-auto p-6 space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-2">
          Enhanced 2-Step File Upload System
        </h1>
        <p className="text-gray-600 dark:text-gray-400">
          Secure, reliable, cross-platform file handling with path-based architecture
        </p>
      </div>

      {/* Context Selection */}
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
        <h2 className="text-xl font-semibold mb-4">Context Selection</h2>
        <select
          value={selectedContext}
          onChange={(e) => setSelectedContext(e.target.value)}
          className="w-full p-3 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
        >
          <option value="">Select a context...</option>
          <option value="context-1">Personal Projects</option>
          <option value="context-2">Work Documents</option>
          <option value="context-3">Research Notes</option>
        </select>
      </div>

      {/* Upload Zones */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Context Auto-Route Upload */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Context Auto-Route</h3>
          <Enhanced2StepDropZone
            destination={destinations.contextAutoRoute}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadProgress={handleUploadProgress}
            disabled={!selectedContext}
            maxFiles={5}
            className="h-32"
          >
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedContext ? 'Auto-routes files by type' : 'Select a context first'}
              </p>
            </div>
          </Enhanced2StepDropZone>
        </div>

        {/* Context Documents Upload */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Context Documents</h3>
          <Enhanced2StepDropZone
            destination={destinations.contextDocuments}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadProgress={handleUploadProgress}
            disabled={!selectedContext}
            acceptedTypes={['application/pdf', 'text/*', 'application/json']}
            className="h-32"
          >
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedContext ? 'Documents folder' : 'Select a context first'}
              </p>
            </div>
          </Enhanced2StepDropZone>
        </div>

        {/* Context Images Upload */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Context Images</h3>
          <Enhanced2StepDropZone
            destination={destinations.contextImages}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadProgress={handleUploadProgress}
            disabled={!selectedContext}
            acceptedTypes={['image/*']}
            className="h-32"
          >
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {selectedContext ? 'Images folder' : 'Select a context first'}
              </p>
            </div>
          </Enhanced2StepDropZone>
        </div>

        {/* Shared Dropbox Upload */}
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Shared Dropbox</h3>
          <Enhanced2StepDropZone
            destination={destinations.sharedDropbox}
            onUploadComplete={handleUploadComplete}
            onUploadError={handleUploadError}
            onUploadProgress={handleUploadProgress}
            className="h-32"
          >
            <div className="text-center">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                Shared across all contexts
              </p>
            </div>
          </Enhanced2StepDropZone>
        </div>
      </div>

      {/* Progress Indicator */}
      {isUploading && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <h3 className="text-lg font-semibold mb-4">Upload Progress</h3>
          <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-4">
            <div 
              className="bg-blue-500 h-4 rounded-full transition-all duration-300"
              style={{ width: `${uploadProgress}%` }}
            />
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2">
            {uploadProgress}% complete
          </p>
        </div>
      )}

      {/* Results */}
      {uploadResults.length > 0 && (
        <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-sm">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold">Upload Results</h3>
            <button
              onClick={clearResults}
              className="px-4 py-2 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              Clear Results
            </button>
          </div>
          
          <div className="space-y-4">
            {uploadResults.map((result, index) => (
              <div key={index} className="border border-gray-200 dark:border-gray-600 rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    result.success 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                  }`}>
                    {result.success ? 'Success' : 'Failed'}
                  </span>
                  <span className="text-sm text-gray-500">
                    {result.files.length} files
                  </span>
                </div>
                
                {result.success && result.files.map((file, fileIndex) => (
                  <div key={fileIndex} className="text-sm text-gray-600 dark:text-gray-400 mb-1">
                    <span className="font-medium">{file.originalName}</span>
                    <span className="mx-2">→</span>
                    <span className="font-mono text-xs">{file.relativePath}</span>
                    {file.autoRouteFolder && (
                      <span className="ml-2 px-1 py-0.5 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded text-xs">
                        {file.autoRouteFolder}
                      </span>
                    )}
                  </div>
                ))}
                
                {!result.success && result.errors && (
                  <div className="text-sm text-red-600 dark:text-red-400">
                    {result.errors.map((error, errorIndex) => (
                      <div key={errorIndex}>
                        <span className="font-medium">{error.filename}:</span> {error.message}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Technical Details */}
      <div className="bg-gray-50 dark:bg-gray-900 rounded-lg p-6">
        <h3 className="text-lg font-semibold mb-4">How It Works</h3>
        <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">1</span>
            <div>
              <p className="font-medium">Secure Path Extraction</p>
              <p>Files dropped → Native dialog for secure path access → Temporary session storage</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">2</span>
            <div>
              <p className="font-medium">Cross-Platform Path Translation</p>
              <p>OS-specific path normalization → Vault structure mapping → Auto-routing by file type</p>
            </div>
          </div>
          <div className="flex items-start space-x-3">
            <span className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-xs font-bold">3</span>
            <div>
              <p className="font-medium">Reliable File Operations</p>
              <p>Direct file copy using paths → No content through IPC → Automatic indexing</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Enhanced2StepFileUploadExample
