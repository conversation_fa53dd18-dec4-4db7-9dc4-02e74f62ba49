import React, { useState, useEffect } from 'react'
import { useParams, useSearchParams } from 'react-router-dom'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import {
  faFileText, faFilePdf, faFileCode, faFileWord, faFileExcel, faFilePowerpoint,
  faFileImage, faFileVideo, faFileAudio, faFileZipper, faFile, faFolder,
  faSitemap, faClock, faThLarge, faSortUp, faSortDown, faSort, faFileLines,
  faChevronDown, faChevronRight, faRefresh, faList, faBrain
} from '@fortawesome/free-solid-svg-icons'

// import { ArtifactsSidebar } from '../components/artifacts/ArtifactsSidebar'

import { FileTreeNode } from '../types'
import { vaultUIManager } from '../services/vaultUIManager'
import { contextVaultService } from '../services/contextVaultService';
import { fileRoutingService } from '../services/fileRoutingService';
import { ContextVaultSelector } from '../components/ContextVaultSelector'



// FileTreeNode interface is now imported from types

interface ViewModeState {
  currentMode: 'explorer' | 'master'
  showArtifacts: boolean
  artifactsExpanded: boolean
}



const FilesPage: React.FC = () => {
  const { contextId } = useParams()
  const [searchParams] = useSearchParams()
  // const { artifactsVisible } = useAppStore()

  // Get context from URL params
  const contextFromUrl = searchParams.get('context') || contextId

  // State for vault data
  const [fileTree, setFileTree] = useState<FileTreeNode[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [dragOver, setDragOver] = useState<string | null>(null)

  // State for UI
  const [viewMode, setViewMode] = useState<ViewModeState>({
    currentMode: 'explorer', // Start with explorer, will switch to master when master.md is found
    showArtifacts: false,
    artifactsExpanded: false
  })

  const [selectedFile, setSelectedFile] = useState<string | null>(null)
  const [selectedFolder, setSelectedFolder] = useState<string | null>(null)
  const [selectedContextId, setSelectedContextId] = useState<string | null>(null)
  const [folderFiles, setFolderFiles] = useState<FileTreeNode[]>([])
  const [folderLoading, setFolderLoading] = useState<boolean>(false)
  const [expandedFolders, setExpandedFolders] = useState<Set<string>>(new Set())
  const [masterContent, setMasterContent] = useState<string>('')
  const [masterLoading, setMasterLoading] = useState(false)

  // State for remembering last context vault (for Master mode UX)
  const [lastContextVaultId, setLastContextVaultId] = useState<string | null>(null)
  const [pendingMasterMode, setPendingMasterMode] = useState<boolean>(false)

  // Load vault file tree on mount
  useEffect(() => {
    console.log('FilesPage mounted, loading file tree...')
    loadFileTree()
  }, []) // Run once on mount

  // Reload when context changes
  useEffect(() => {
    if (contextFromUrl) {
      console.log('Context changed to:', contextFromUrl, 'reloading file tree...')
      loadFileTree()
    }
  }, [contextFromUrl])

  // Reload when selected context changes (including when it becomes empty for shared dropbox)
  useEffect(() => {
    console.log('Selected context changed to:', selectedContextId || 'shared dropbox', 'reloading file tree...')

    // Track last context vault for Master mode UX
    if (selectedContextId) {
      setLastContextVaultId(selectedContextId)
      console.log('Remembering last context vault:', selectedContextId)

      // If we were pending master mode, switch to it now
      if (pendingMasterMode) {
        console.log('Context switched, now switching to Master mode')
        setPendingMasterMode(false)
        setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
        setSelectedFile('master.md')
      } else {
        // When selecting a context vault from menu, default to Master mode
        console.log('Context vault selected from menu, switching to Master mode')
        setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
        setSelectedFile('master.md')
      }
    }

    // Auto-switch to Explorer mode when shared dropbox is selected
    if (!selectedContextId) {
      console.log('Shared dropbox selected, switching to Explorer mode')
      setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))
      setPendingMasterMode(false) // Clear any pending master mode
      setSelectedFile(null) // Clear selected file
    }

    loadFileTree()
  }, [selectedContextId, pendingMasterMode])

  // Debug: Log when fileTree changes
  useEffect(() => {
    console.log('File tree updated:', fileTree.length, 'items')
    console.log('Expanded folders:', Array.from(expandedFolders))
    console.log('Selected file:', selectedFile)
    console.log('Selected folder:', selectedFolder)
    console.log('Current view mode:', viewMode.currentMode)
  }, [fileTree, expandedFolders, selectedFile, selectedFolder, viewMode])

  // Auto-select first folder when file tree loads
  useEffect(() => {
    if (fileTree.length > 0 && !selectedFolder) {
      // Find the first context folder (skip vault folders)
      const findFirstContext = (nodes: FileTreeNode[]): string | null => {
        for (const node of nodes) {
          if (node.type === 'folder' && node.children) {
            // Look for context folders within vault folders
            for (const child of node.children) {
              if (child.type === 'folder') {
                return child.path
              }
            }
          }
        }
        return null
      }

      const firstContextPath = findFirstContext(fileTree)
      if (firstContextPath) {
        console.log('Auto-selecting first context folder:', firstContextPath)
        setSelectedFolder(firstContextPath)
        loadFolderFiles(firstContextPath)
      }
    }
  }, [fileTree, selectedFolder])

  const loadFileTree = async () => {
    try {
      setLoading(true)
      setError(null)

      // Use selectedContextId if available, otherwise fall back to contextFromUrl
      const contextId = selectedContextId || contextFromUrl
      console.log('Loading file tree for context:', contextId)

      const tree = await vaultUIManager.getFileTree(contextId || undefined)
      console.log('Raw file tree from vaultUIManager:', tree)
      setFileTree(tree)

      // Auto-expand and select first master.md (like GitHub README)
      if (tree && tree.length > 0) {
        console.log('Processing file tree with', tree.length, 'root nodes')
        const expandedPaths = new Set<string>()
        let firstMasterPath: string | null = null

        // Function to recursively find master.md and expand folders
        const findMasterAndExpand = (nodes: FileTreeNode[], parentPath: string = '') => {
          console.log('Processing nodes at level:', parentPath, 'nodes:', nodes.length)
          for (const node of nodes) {
            console.log('Processing node:', node.name, 'type:', node.type, 'path:', node.path)
            if (node.type === 'folder') {
              // Auto-expand all vault and context folders
              expandedPaths.add(node.path)
              console.log('Added to expanded paths:', node.path)

              if (node.children) {
                findMasterAndExpand(node.children, node.path)
              }
            } else if (node.type === 'file' && node.name === 'master.md' && !firstMasterPath) {
              // Found the first master.md
              firstMasterPath = node.path
              console.log('Found first master.md at:', firstMasterPath)
            } else if (node.type === 'file') {
              console.log('Found file:', node.name, 'at:', node.path)
            }
          }
        }

        findMasterAndExpand(tree)

        // Update state with expanded folders and selected master.md
        console.log('Setting expanded folders:', Array.from(expandedPaths))
        setExpandedFolders(expandedPaths)

        if (firstMasterPath) {
          console.log('Setting selected file and switching to master mode:', firstMasterPath)
          setSelectedFile(firstMasterPath)

          // Use setTimeout to ensure state updates are processed
          setTimeout(() => {
            setViewMode(prev => {
              console.log('Switching view mode from', prev.currentMode, 'to master')
              return { ...prev, currentMode: 'master' }
            })
            // Load the master.md content
            loadMasterContent(firstMasterPath!)
          }, 100)
        } else {
          console.log('No master.md found in file tree')
        }
      }
    } catch (err: any) {
      console.error('Error loading file tree:', err)
      setError(err.message || 'Failed to load file tree')
    } finally {
      setLoading(false)
    }
  }

  // Add CSS styles for file tree interactions
  React.useEffect(() => {
    const style = document.createElement('style')
    style.textContent = `
      .file-tree-item {
        transition: all 0.2s ease;
      }
      .file-tree-item:hover {
        background-color: rgba(138, 176, 187, 0.1);
      }
      .file-tree-item.selected {
        background-color: rgba(138, 176, 187, 0.2);
        border-left: 2px solid #8AB0BB;
      }
      .file-tree-item.drop-target {
        background-color: rgba(138, 176, 187, 0.2);
        border: 2px dashed #8AB0BB;
      }
      .line-clamp-2 {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
      }
    `
    document.head.appendChild(style)

    return () => {
      document.head.removeChild(style)
    }
  }, [])

  const toggleFolder = (path: string) => {
    setExpandedFolders(prev => {
      const newSet = new Set(prev)
      if (newSet.has(path)) {
        newSet.delete(path)
      } else {
        newSet.add(path)
      }
      return newSet
    })

    // When toggling a folder, also select it to show its contents in the explorer
    setSelectedFolder(path)
    loadFolderFiles(path)
  }

  const selectFile = (path: string) => {
    setSelectedFile(path)

    // If selecting master.md, load its content and switch to master mode
    if (path.endsWith('master.md')) {
      loadMasterContent(path)
      setViewMode(prev => ({ ...prev, currentMode: 'master', showArtifacts: true }))
    } else {
      // For any other file, switch to explorer mode
      setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))
    }
  }

  const selectFolder = (path: string) => {
    setSelectedFolder(path)
    loadFolderFiles(path)
    // Switch to explorer mode when selecting a folder
    setViewMode(prev => ({ ...prev, currentMode: 'explorer', showArtifacts: false }))
  }

  const loadMasterContent = async (filePath: string) => {
    try {
      setMasterLoading(true)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readFile) {
        setMasterContent('# Master Document\n\nContent not available in development mode.')
        return
      }

      const result = await window.electronAPI.vault.readFile(filePath)
      if (result.success && result.content) {
        setMasterContent(result.content)
      } else {
        setMasterContent('# Error\n\nFailed to load master document content.')
      }
    } catch (error) {
      console.error('Error loading master content:', error)
      setMasterContent('# Error\n\nFailed to load master document content.')
    } finally {
      setMasterLoading(false)
    }
  }

  const loadFolderFiles = async (folderPath: string) => {
    try {
      setFolderLoading(true)
      console.log('Loading files from folder:', folderPath)

      // Check if electronAPI is available
      if (!window.electronAPI?.vault?.readDirectory) {
        console.warn('ElectronAPI not available, using mock data')
        setFolderFiles([])
        return
      }

      const result = await window.electronAPI.vault.readDirectory(folderPath)
      if (result.success && result.items) {
        // Convert directory items to FileTreeNode format
        const files: FileTreeNode[] = result.items
          .filter(item => !item.name.startsWith('.')) // Filter out hidden files
          .map(item => ({
            type: item.isDirectory ? 'folder' : 'file',
            name: item.name,
            path: item.path,
            icon: item.isDirectory ? faFolder : getFileIcon(item.name),
            color: item.isDirectory ? 'text-supplement2' : getFileColor(item.name),
            size: item.size,
            modified: item.modified
          }))

        setFolderFiles(files)
        console.log('Folder files loaded successfully:', files.length, 'items')
      } else {
        console.error('Failed to load folder files:', result.error)
        setFolderFiles([])
      }
    } catch (error) {
      console.error('Error loading folder files:', error)
      setFolderFiles([])
    } finally {
      setFolderLoading(false)
    }
  }

  const getFileIcon = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return faFileText
      case 'pdf': return faFilePdf
      case 'txt': return faFileText
      case 'docx': case 'doc': return faFileWord
      case 'xlsx': case 'xls': return faFileExcel
      case 'pptx': case 'ppt': return faFilePowerpoint
      case 'png': case 'jpg': case 'jpeg': case 'gif': return faFileImage
      case 'mp4': case 'avi': case 'mov': return faFileVideo
      case 'mp3': case 'wav': case 'flac': return faFileAudio
      case 'zip': case 'rar': case '7z': return faFileZipper
      case 'js': case 'ts': case 'jsx': case 'tsx': case 'py': case 'java': case 'cpp': case 'c': return faFileCode
      case 'json': return faFileCode
      default: return faFile
    }
  }

  const getFileColor = (fileName: string) => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return 'text-primary'
      case 'pdf': return 'text-red-400'
      case 'docx': case 'doc': return 'text-blue-400'
      case 'xlsx': case 'xls': return 'text-green-400'
      case 'pptx': case 'ppt': return 'text-orange-400'
      case 'png': case 'jpg': case 'jpeg': case 'gif': return 'text-purple-400'
      case 'js': case 'ts': case 'jsx': case 'tsx': return 'text-yellow-400'
      case 'py': return 'text-green-400'
      case 'json': return 'text-yellow-400'
      default: return 'text-gray-400'
    }
  }

  // File drop handlers
  const handleDragOver = (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(folderPath || 'general')
  }

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)
  }

  const handleFileDrop = async (e: React.DragEvent, folderPath?: string) => {
    e.preventDefault()
    e.stopPropagation()
    setDragOver(null)

    const files = Array.from(e.dataTransfer.files)
    if (files.length === 0) return

    const currentContextId = selectedContextId || contextFromUrl;
    if (!currentContextId) return;

    try {
      // Use FileRoutingService for context-aware file dropping
      const destination = {
        contextId: currentContextId,
        path: folderPath, // Specific folder path if provided
        autoRoute: !folderPath // Only auto-route if no specific folder is provided
      };

      const result = await fileRoutingService.handleFileDrop(files, destination);

      if (result.success) {
        console.log(`${result.files.length} files successfully added to ${folderPath || 'context root'}`);
        // Refresh the folder content if dropped into a specific folder
        if (folderPath) {
          await loadFolderFiles(folderPath);
        } else {
          // Otherwise, refresh the whole tree
          await loadFileTree();
        }
      } else {
        console.error('File drop failed:', result.errors);
        // Show error to user if needed
        const errorMessage = result.errors?.[0]?.message || 'Failed to upload files';
        console.error('Error dropping files:', errorMessage);
      }
    } catch (error) {
      console.error('Error dropping files:', error);
    }
  }

  const handleModeChange = async (mode: 'explorer' | 'master') => {
    // Handle Master mode UX logic
    if (mode === 'master') {
      // If currently in shared dropbox, switch to a context vault
      if (!selectedContextId) {
        console.log('Switching to Master mode from shared dropbox')

        // Try to use last context vault
        let targetContextId = lastContextVaultId

        // If no last context, fallback to personal vault's first context
        if (!targetContextId) {
          const vaults = contextVaultService.getCurrentVaults()
          const personalVault = vaults.find(v => v.name.toLowerCase().includes('personal'))
          if (personalVault && personalVault.contexts.length > 0) {
            targetContextId = personalVault.contexts[0].id
            console.log('Fallback to personal vault first context:', targetContextId)
          } else {
            // If no personal vault, use any first available context
            const allContexts = contextVaultService.getAllContexts()
            if (allContexts.length > 0) {
              targetContextId = allContexts[0].id
              console.log('Fallback to first available context:', targetContextId)
            }
          }
        }

        // Switch to the target context
        if (targetContextId) {
          console.log('Switching to context for Master mode:', targetContextId)
          setPendingMasterMode(true) // Set flag to switch to master mode after context change
          contextVaultService.setSelectedContext(targetContextId)
          // The context change will be handled by the useEffect
          return // Don't set view mode yet, let the context change handle it
        } else {
          console.warn('No context available for Master mode')
          // Stay in explorer mode if no contexts available
          return
        }
      }

      // Auto-select master.md when switching to master mode
      setSelectedFile('master.md')
    }

    setViewMode(prev => ({
      ...prev,
      currentMode: mode,
      showArtifacts: mode === 'master'
    }))
  }

  const renderFileTreeNode = (node: FileTreeNode, level: number = 0) => {
    const isExpanded = expandedFolders.has(node.path)
    const isSelected = selectedFile === node.path
    const marginLeft = level * 16 // 4 * 4 = 16px per level

    // Debug: Log expansion check
    if (node.type === 'folder') {
      console.log(`Checking expansion for ${node.name} (${node.path}): ${isExpanded}`)
      console.log('Available expanded paths:', Array.from(expandedFolders))
    }

    return (
      <div key={node.path}>
        <div
          className={`
            file-tree-item p-2 rounded cursor-pointer flex items-center gap-2 transition-all relative min-w-0
            ${isSelected ? 'selected bg-primary/20 border border-primary/30' : 'hover:bg-gray-700/50'}
            ${dragOver === node.path && node.type === 'folder' ? 'drop-target' : ''}
          `}
          style={{ marginLeft: `${marginLeft}px` }}
          onDragOver={node.type === 'folder' ? (e) => handleDragOver(e, node.path) : undefined}
          onDragLeave={node.type === 'folder' ? handleDragLeave : undefined}
          onDrop={node.type === 'folder' ? (e) => handleFileDrop(e, node.path) : undefined}
          onClick={() => {
            if (node.type === 'folder') {
              toggleFolder(node.path)
              selectFolder(node.path)
              // Switch to explorer mode when folder is clicked (exit master.md view)
              setViewMode(prev => ({ ...prev, currentMode: 'explorer' }))
            } else {
              selectFile(node.path)
            }
          }}
        >
          {node.type === 'folder' && (
            <FontAwesomeIcon
              icon={isExpanded ? faChevronDown : faChevronRight}
              className="text-gray-400 text-xs w-3 flex-shrink-0"
            />
          )}
          {node.type === 'file' && <div className="w-3 flex-shrink-0"></div>}

          <FontAwesomeIcon icon={node.icon} className={`text-sm ${node.color} flex-shrink-0`} />

          <span
            className={`text-sm flex-1 min-w-0 truncate ${isSelected ? 'text-primary font-medium' : node.color === 'text-primary' ? 'text-primary font-medium' : 'text-supplement1'}`}
            title={node.name}
          >
            {node.name}
          </span>
          
          <div className="ml-auto flex-shrink-0">
            {/* File count badge would go here if available */}
            {isSelected && node.type === 'file' && (
              <div className="w-2 h-2 bg-primary rounded-full"></div>
            )}
          </div>
        </div>
        
        {node.type === 'folder' && isExpanded && node.children && (
          <div>
            {node.children.map(child => renderFileTreeNode(child, level + 1))}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="flex-1 flex flex-col h-full bg-gray-900 text-white">
      {/* Shared Dropbox header hidden per user request */}
      {/* {viewMode.currentMode === 'explorer' && (
        <div className="p-4">
          <h2 className="text-xl font-bold mb-4">Shared Dropbox</h2>

        </div>
      )} */}

      {/* Master.md view */}
      <div className="flex-1 flex bg-gray-900 h-0">
        
        {/* Left Column - File Tree (20%) */}
        <div className="w-1/5 bg-gray-800 border-r border-tertiary/50 flex flex-col h-full">
          
          {/* Context Vault Selector */}
          <ContextVaultSelector
            selectedContextId={selectedContextId || undefined}
            onContextChange={setSelectedContextId}
          />

          {/* View Toggle Buttons */}
          <div className="p-3 border-b border-tertiary/50">
            <div className="flex gap-2">
              <button 
                onClick={() => handleModeChange('explorer')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'explorer' 
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80' 
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faSitemap} className="text-sm" />
                <span className="text-xs font-medium">Explorer</span>
              </button>
              <button
                onClick={() => handleModeChange('master')}
                className={`flex-1 flex items-center justify-center gap-2 p-2 rounded-lg transition-colors ${
                  viewMode.currentMode === 'master'
                    ? 'bg-secondary text-gray-900 hover:bg-secondary/80'
                    : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
                }`}
              >
                <FontAwesomeIcon icon={faBrain} className="text-sm" />
                <span className="text-xs font-medium">Master</span>
              </button>
            </div>
          </div>
          
          {/* File Tree - Scrollable with proper wrapping */}
          <div className="flex-1 overflow-y-auto overflow-x-hidden p-2 min-h-0 max-h-full">
            {loading ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <FontAwesomeIcon icon={faRefresh} className="text-primary text-lg mb-2 animate-spin" />
                  <p className="text-sm text-supplement1">Loading files...</p>
                </div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center h-32">
                <div className="text-center">
                  <p className="text-sm text-secondary mb-2">Error: {error}</p>
                  <button
                    onClick={loadFileTree}
                    className="px-3 py-1 bg-primary text-gray-900 rounded text-xs hover:bg-primary/80 transition-colors"
                  >
                    Retry
                  </button>
                </div>
              </div>
            ) : (
              <div className="space-y-1">
                {fileTree.map(node => renderFileTreeNode(node))}
              </div>
            )}
          </div>
        </div>
        
        {/* Right Column Content */}
        {(() => {
          console.log('Rendering right column, viewMode.currentMode:', viewMode.currentMode)
          console.log('Master content length:', masterContent.length)
          console.log('Master loading:', masterLoading)
          return viewMode.currentMode === 'master' ? (
            <MasterMode
              content={masterContent}
              loading={masterLoading}
              selectedFile={selectedFile}
            />
          ) : (
            <ExplorerMode
              selectedFolder={selectedFolder}
              folderFiles={folderFiles}
              folderLoading={folderLoading}
            />
          )
        })()}
      </div>
    </div>
  )
}

// Master Mode Component
interface MasterModeProps {
  content: string
  loading: boolean
  selectedFile: string | null
}

const MasterMode: React.FC<MasterModeProps> = ({ content, loading, selectedFile }) => {
  // Extract title from content (first # heading)
  const getTitle = (content: string) => {
    const match = content.match(/^#\s+(.+)$/m)
    return match ? match[1] : 'Master Document'
  }

  // Simple markdown to HTML conversion for basic display
  const renderMarkdown = (content: string) => {
    if (!content) return ''

    return content
      .replace(/^# (.+)$/gm, '<h1 class="text-2xl font-bold text-supplement1 mb-4">$1</h1>')
      .replace(/^## (.+)$/gm, '<h2 class="text-xl font-semibold text-supplement1 mb-3 mt-6">$2</h2>')
      .replace(/^### (.+)$/gm, '<h3 class="text-lg font-medium text-supplement2 mb-2 mt-4">$3</h3>')
      .replace(/^\* (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/^- (.+)$/gm, '<li class="text-gray-400 ml-4">• $1</li>')
      .replace(/\*\*(.+?)\*\*/g, '<strong class="text-supplement1">$1</strong>')
      .replace(/\*(.+?)\*/g, '<em class="text-gray-300">$1</em>')
      .replace(/`(.+?)`/g, '<code class="bg-gray-800 px-1 py-0.5 rounded text-primary text-sm">$1</code>')
      .replace(/\n\n/g, '</p><p class="text-gray-400 mb-4">')
      .replace(/^(?!<[h|l])/gm, '<p class="text-gray-400 mb-4">')
      .replace(/<p class="text-gray-400 mb-4">(<[h|l])/g, '$1')
  }

  return (
    <div className="flex-1 flex">
      {/* Master Document Preview */}
      <div className="flex-1 overflow-y-auto p-6">
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <FontAwesomeIcon icon={faRefresh} className="text-primary text-2xl mb-4 animate-spin" />
              <p className="text-supplement1">Loading master document...</p>
            </div>
          </div>
        ) : (
          <div className="markdown-content">
            {/* Document Header */}
            <div className="flex items-center gap-3 mb-6 pb-4 border-b border-tertiary/30">
              <div className="w-10 h-10 bg-primary/20 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faFileText} className="text-primary text-lg" />
              </div>
              <div>
                <h1 className="text-supplement1 text-xl font-semibold">{getTitle(content)}</h1>
                <div className="flex items-center gap-4 text-xs text-gray-400 mt-1">
                  <span className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faClock} className="text-xs" />
                    Just updated
                  </span>
                  <span className="flex items-center gap-1">
                    <FontAwesomeIcon icon={faFileText} className="text-xs" />
                    {selectedFile ? selectedFile.split('/').pop() : 'master.md'}
                  </span>
                </div>
              </div>
            </div>

            {/* Markdown Content */}
            <div
              className="prose prose-invert max-w-none"
              dangerouslySetInnerHTML={{ __html: renderMarkdown(content) }}
            />
          </div>
        )}
      </div>

      {/* Artifacts Sidebar will be rendered by ArtifactsSidebar component */}
      {/* <ArtifactsSidebar /> */}
    </div>
  )
}

// Explorer Mode Component
interface ExplorerModeProps {
  selectedFolder: string | null
  folderFiles: FileTreeNode[]
  folderLoading: boolean
}

const ExplorerMode: React.FC<ExplorerModeProps> = ({ selectedFolder, folderFiles, folderLoading }) => {
  const [viewMode, setViewMode] = useState<'list' | 'grid'>('list')
  const [sortBy, setSortBy] = useState<'name' | 'modified' | 'type' | 'size'>('name')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('asc')
  const [selectedFiles, setSelectedFiles] = useState<Set<string>>(new Set())

  // If no folder selected, show instruction
  if (!selectedFolder) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center max-w-md">
          <FontAwesomeIcon icon={faFolder} className="text-supplement1 text-6xl mb-6" />
          <h2 className="text-2xl font-semibold text-supplement1 mb-4">Select a Folder</h2>
          <p className="text-gray-400 mb-8 leading-relaxed">
            Click on a folder in the tree view to explore its contents in the file explorer.
          </p>
        </div>
      </div>
    )
  }

  // Show loading state
  if (folderLoading) {
    return (
      <div className="flex-1 flex items-center justify-center">
        <div className="text-center">
          <FontAwesomeIcon icon={faRefresh} className="text-primary text-3xl mb-4 animate-spin" />
          <p className="text-supplement1 text-lg">Loading folder contents...</p>
        </div>
      </div>
    )
  }

  // Use the real folder files from the selected folder
  const allFiles = folderFiles

  // Sort files
  const sortedFiles = [...allFiles].sort((a, b) => {
    let comparison = 0

    switch (sortBy) {
      case 'name':
        comparison = a.name.localeCompare(b.name)
        break
      case 'modified':
        comparison = new Date(a.modified || 0).getTime() - new Date(b.modified || 0).getTime()
        break
      case 'type':
        const aType = a.type === 'folder' ? 'Folder' : getFileType(a.name)
        const bType = b.type === 'folder' ? 'Folder' : getFileType(b.name)
        comparison = aType.localeCompare(bType)
        break
      case 'size':
        comparison = (a.size || 0) - (b.size || 0)
        break
    }

    return sortOrder === 'asc' ? comparison : -comparison
  })

  const getFileType = (fileName: string): string => {
    const ext = fileName.split('.').pop()?.toLowerCase()
    switch (ext) {
      case 'md': return 'Markdown File'
      case 'pdf': return 'PDF Document'
      case 'txt': return 'Text File'
      case 'docx': case 'doc': return 'Word Document'
      case 'xlsx': case 'xls': return 'Excel Spreadsheet'
      case 'pptx': case 'ppt': return 'PowerPoint Presentation'
      case 'png': case 'jpg': case 'jpeg': case 'gif': return 'Image File'
      case 'json': return 'JSON File'
      default: return 'File'
    }
  }

  const formatFileSize = (bytes?: number): string => {
    if (!bytes) return '-'
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(1024))
    return `${(bytes / Math.pow(1024, i)).toFixed(1)} ${sizes[i]}`
  }

  const formatDate = (dateString?: string): string => {
    if (!dateString) return '-'
    const date = new Date(dateString)
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffHours / 24)
    const diffWeeks = Math.floor(diffDays / 7)

    if (diffHours < 1) return 'Just now'
    if (diffHours < 24) return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`
    if (diffDays < 7) return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`
    if (diffWeeks < 4) return `${diffWeeks} week${diffWeeks > 1 ? 's' : ''} ago`
    return date.toLocaleDateString()
  }

  const handleSort = (column: typeof sortBy) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  const toggleFileSelection = (filePath: string) => {
    const newSelection = new Set(selectedFiles)
    if (newSelection.has(filePath)) {
      newSelection.delete(filePath)
    } else {
      newSelection.add(filePath)
    }
    setSelectedFiles(newSelection)
  }

  const getSortIcon = (column: typeof sortBy) => {
    if (sortBy !== column) return faSort
    return sortOrder === 'asc' ? faSortUp : faSortDown
  }

  return (
    <div className="flex-1 bg-gray-900 flex flex-col">
      {/* Explorer Header */}
      <div className="border-b border-tertiary/50 p-4 flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h2 className="text-lg font-semibold text-supplement1">File Explorer</h2>
          <div className="flex items-center gap-2">
            <span className="text-sm text-gray-400">
              {selectedFolder ? selectedFolder.split('/').pop() || selectedFolder : 'No folder selected'}
            </span>
            <span className="text-xs text-gray-500">•</span>
            <span className="text-sm text-gray-400">
              {allFiles.length} item{allFiles.length !== 1 ? 's' : ''}
            </span>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => setViewMode('list')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'list'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={faList} className="text-sm" />
          </button>
          <button
            onClick={() => setViewMode('grid')}
            className={`p-2 rounded-lg transition-colors ${
              viewMode === 'grid'
                ? 'bg-primary/20 text-primary'
                : 'bg-gray-700/50 hover:bg-gray-700 text-supplement1'
            }`}
          >
            <FontAwesomeIcon icon={faThLarge} className="text-sm" />
          </button>
        </div>
      </div>

      {/* File Content */}
      <div className="flex-1 overflow-y-auto">
        {viewMode === 'list' ? (
          <table className="w-full">
            <thead className="bg-gray-800 border-b border-tertiary/50 sticky top-0">
              <tr>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('name')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Name
                    <FontAwesomeIcon icon={getSortIcon('name')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('modified')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Date Modified
                    <FontAwesomeIcon icon={getSortIcon('modified')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('type')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Type
                    <FontAwesomeIcon icon={getSortIcon('type')} className="text-xs" />
                  </button>
                </th>
                <th className="text-left p-4 text-sm font-medium text-supplement1">
                  <button
                    onClick={() => handleSort('size')}
                    className="flex items-center gap-2 hover:text-primary transition-colors"
                  >
                    Size
                    <FontAwesomeIcon icon={getSortIcon('size')} className="text-xs" />
                  </button>
                </th>
              </tr>
            </thead>
            <tbody>
              {sortedFiles.map((file) => (
                <tr
                  key={file.path}
                  className={`border-b border-gray-800 cursor-pointer transition-colors hover:bg-gray-800/50 ${
                    selectedFiles.has(file.path) ? 'bg-primary/10 border-l-2 border-l-primary' : ''
                  }`}
                  onClick={() => toggleFileSelection(file.path)}
                >
                  <td className="p-4">
                    <div className="flex items-center gap-3">
                      <FontAwesomeIcon
                        icon={file.type === 'folder' ? faFolder : faFileLines}
                        className={`text-lg ${
                          file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                        }`}
                      />
                      <span className="text-sm text-supplement1 font-medium">{file.name}</span>
                    </div>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatDate(file.modified)}</span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">
                      {file.type === 'folder' ? 'Folder' : getFileType(file.name)}
                    </span>
                  </td>
                  <td className="p-4">
                    <span className="text-sm text-gray-400">{formatFileSize(file.size)}</span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        ) : (
          <div className="p-4 grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4">
            {sortedFiles.map((file) => (
              <div
                key={file.path}
                className={`p-4 bg-gray-800/50 rounded-lg hover:bg-gray-700/50 cursor-pointer transition-colors border ${
                  selectedFiles.has(file.path)
                    ? 'border-primary/50 bg-primary/10'
                    : 'border-transparent hover:border-primary/30'
                }`}
                onClick={() => toggleFileSelection(file.path)}
              >
                <div className="flex flex-col items-center text-center">
                  <div className="w-12 h-12 bg-primary/20 rounded-lg flex items-center justify-center mb-3">
                    <FontAwesomeIcon
                      icon={file.type === 'folder' ? faFolder : faFileLines}
                      className={`text-xl ${
                        file.type === 'folder' ? 'text-supplement2' : 'text-primary'
                      }`}
                    />
                  </div>
                  <h4 className="text-sm font-medium text-supplement1 mb-1 truncate w-full">{file.name}</h4>
                  <p className="text-xs text-gray-400">{formatFileSize(file.size)} • {file.type === 'folder' ? 'Folder' : getFileType(file.name)}</p>
                  <p className="text-xs text-gray-500 mt-1">{formatDate(file.modified)}</p>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

export default FilesPage
