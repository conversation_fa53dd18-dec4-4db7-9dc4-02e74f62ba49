import { app, BrowserWindow, ipcMain, IpcMainInvokeEvent, dialog } from 'electron'
import { autoUpdater } from 'electron-updater'
import path from 'path'
import { isDev } from './utils'
import { DatabaseManager } from './database'
import { FileSystemManager } from './fileSystem'
import { APIRegistry } from './api/APIRegistry'
import { UniversalPluginManager } from './plugins/PluginManager'
import { PluginCapability } from './plugins/types'
import { startServer as startApiServer, stopServer as stopApiServer } from './api/server/server'
import { FileRoutingApiServer } from './api/server/fileRoutingApiServer'
// TODO: Removed FileProcessingDiagnostics import - module marked for removal

class App {
  public mainWindow: BrowserWindow | null = null
  private db: DatabaseManager
  private fileSystem: FileSystemManager
  private apiRegistry: APIRegistry
  private pluginManager: UniversalPluginManager
  private apiServer: FileRoutingApiServer | null = null

  constructor() {
    this.db = new DatabaseManager()
    this.fileSystem = new FileSystemManager(this.db)
    
    // Initialize API registry with middleware
    this.apiRegistry = new APIRegistry({
      logging: {
        logRequests: true,
        logResponses: false,
        logErrors: true,
        logPerformance: true,
        maxLogLength: 500
      },
      rateLimiting: {
        maxRequests: 200,
        windowMs: 60000 // 1 minute
      },
      security: {
        allowedOrigins: isDev ? ['file://', 'http://localhost:5173'] : ['file://'],
        requireAuth: false
      },
      errorHandling: {
        logErrors: true,
        sanitizeErrors: false // Keep detailed errors for development
      }
    })
    
    // Initialize plugin manager
    this.pluginManager = new UniversalPluginManager(this.apiRegistry)
    
    // Add plugin directories
    this.pluginManager.addPluginDirectory(path.join(__dirname, 'plugins'))
    this.pluginManager.addPluginDirectory(path.join(app.getPath('userData'), 'plugins'))
  }

  private createWindow(): void {
    // Following Electron best practices from official documentation
    this.mainWindow = new BrowserWindow({
      width: 1200,
      height: 800,
      minWidth: 800,
      minHeight: 600,
      show: false, // Don't show until ready-to-show event
      frame: false, // Frameless window for custom chrome
      titleBarStyle: process.platform === 'darwin' ? 'hiddenInset' : 'hidden',
      titleBarOverlay: false,
      backgroundColor: '#111827', // Match our app's dark theme (gray-900)
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
        allowRunningInsecureContent: false,
        experimentalFeatures: false,
        preload: path.join(__dirname, 'preload.js'),
        webSecurity: true,
      },
    })

    // Use ready-to-show event for graceful window display (Electron best practice)
    this.mainWindow.once('ready-to-show', () => {
      if (this.mainWindow) {
        this.mainWindow.show()

        // Open DevTools in development after window is shown
        if (isDev) {
          this.mainWindow.webContents.openDevTools()
        }
      }
    })

    if (isDev) {
      this.mainWindow.loadURL('http://localhost:5173')
    } else {
      this.mainWindow.loadFile(path.join(__dirname, '../dist/index.html'))
    }

    this.mainWindow.on('closed', () => {
      this.mainWindow = null
    })
  }

  private validateSender(frame: any): boolean {
    // Validate the sender is from your app
    if (!frame || !frame.url) return false
    return frame.url.startsWith('file://') ||
           frame.url.startsWith('http://localhost:5173')
  }

  private validateInput(value: any, type: string, maxLength?: number): boolean {
    if (value === null || value === undefined) return false
    if (typeof value !== type) return false
    if (type === 'string' && maxLength && value.length > maxLength) return false
    return true
  }

  private async createContextStructure(contextPath: string, contextName: string): Promise<void> {
    const fs = await import('fs')
    const path = await import('path')

    // Create context directory
    await fs.promises.mkdir(contextPath, { recursive: true })

    // Create subdirectories
    await fs.promises.mkdir(path.join(contextPath, 'documents'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'images'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, 'artifacts'), { recursive: true })
    await fs.promises.mkdir(path.join(contextPath, '.context'), { recursive: true })

    // Create master.md
    const masterContent = `# ${contextName}

Welcome to your intelligent context vault! This is your master document that serves as the central hub for organizing and understanding your project.

## Overview
This context vault helps you organize files, conversations, and AI insights in one place.

## Quick Start
1. **Add files** to the \`documents/\` folder to get started
2. **Start a conversation** using the chat interface with this context selected
3. **Organize insights** and progress notes right here in this document

## How It Works
- 📁 **Documents folder**: Store your project files here
- 🖼️ **Images folder**: Add screenshots, diagrams, and visual assets
- 🎯 **Artifacts folder**: Save important outputs from AI conversations
- 🧠 **AI Memory**: The \`.context/\` folder contains AI memory optimized for Gemma models

## AI Insights
*This section will be automatically updated as you add files and have conversations*

## Project Progress
*Use this space to track your progress and key milestones*

---
*Last updated: ${new Date().toLocaleString()}*
*Files: 0 | Conversations: 0*`

    await fs.promises.writeFile(path.join(contextPath, 'master.md'), masterContent, 'utf8')

    // Create context metadata
    const metadata = {
      id: contextName.toLowerCase().replace(/\s+/g, '-'),
      name: contextName,
      created: new Date().toISOString(),
      description: `Context vault for ${contextName}`,
      contextType: 'getting-started'
    }

    await fs.promises.writeFile(
      path.join(contextPath, '.context', 'metadata.json'),
      JSON.stringify(metadata, null, 2),
      'utf8'
    )

    // Create AI memory file
    const memoryContent = `# AI Memory for ${contextName}

## Context Understanding
This context vault was just created and is ready for your first project.

## Key Concepts
*AI will learn and document key concepts from your files and conversations*

## Relationships
*Connections between files, ideas, and conversations will be tracked here*

## Memory Chunks
*Optimized memory chunks for Gemma models will be stored here*

---
*This file is automatically managed by ChatLo's AI system*`

    // TODO: Write the AI memory file (this was removed from registerCoreAPIs)
    // await fs.promises.writeFile(path.join(contextPath, '.context', 'ai-memory.md'), memoryContent, 'utf8')
  }



  private setupIPC(): void {
    ipcMain.handle('get-os-platform', () => {
      return process.platform
    })

    console.log('[MAIN] Setting up IPC handlers...')

    // Register core API categories
    console.log('[MAIN] Registering core APIs...')
    this.registerCoreAPIs()

    // Initialize API registry (sets up IPC handlers)
    console.log('[MAIN] Initializing API registry...')
    this.apiRegistry.initialize()
    console.log('[MAIN] API registry initialized successfully')

    // All database operations are now handled through APIRegistry

    // All intelligence and message operations are now handled through APIRegistry

    // All settings operations are now handled through APIRegistry

    // All file system operations are now handled through APIRegistry







    // All vault operations are now handled through APIRegistry


  }

  private setupAutoUpdater(): void {
    if (!isDev) {
      autoUpdater.checkForUpdatesAndNotify()
    }

    // Auto-updater events
    autoUpdater.on('checking-for-update', () => {
      console.log('Checking for update...')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:checking-for-update')
      }
    })

    autoUpdater.on('update-available', (info) => {
      console.log('Update available:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-available', info)
      }
    })

    autoUpdater.on('update-not-available', (info) => {
      console.log('Update not available')
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-not-available')
      }
    })

    autoUpdater.on('error', (err) => {
      console.error('Auto-updater error:', err)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:error', err.message)
      }
    })

    autoUpdater.on('download-progress', (progressObj) => {
      console.log(`Download progress: ${progressObj.percent}%`)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:download-progress', progressObj)
      }
    })

    autoUpdater.on('update-downloaded', (info) => {
      console.log('Update downloaded:', info.version)
      if (this.mainWindow) {
        this.mainWindow.webContents.send('updater:update-downloaded', info)
      }
    })
  }

  // Register core API endpoints
  private registerCoreAPIs(): void {
    try {
      console.log('[MAIN] Starting registerCoreAPIs...')

      // Database APIs
      console.log('[MAIN] Registering database APIs...')
      this.apiRegistry.registerCategory('db')

    // Conversation endpoints
    this.apiRegistry.registerEndpoint('db', 'getConversations',
      () => this.db.getConversations(),
      { description: 'Get all conversations' }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversation',
      (id: string) => this.db.getConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get a specific conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'createConversation',
      (title: string) => this.db.createConversation(title),
      {
        validator: (title: string) => {
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Create a new conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateConversation',
      (id: string, title: string) => this.db.updateConversation(id, title),
      {
        validator: (id: string, title: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!this.validateInput(title, 'string', 200)) throw new Error('Invalid conversation title')
        },
        description: 'Update a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'deleteConversation',
      (id: string) => this.db.deleteConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Delete a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'togglePinConversation',
      (id: string) => this.db.togglePinConversation(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Toggle pin status of a conversation'
      }
    )

    // Message endpoints
    this.apiRegistry.registerEndpoint('db', 'getMessages',
      (conversationId: string) => this.db.getMessages(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get messages for a conversation'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addMessage',
      (conversationId: string, message: any) => this.db.addMessage(conversationId, message),
      {
        validator: (conversationId: string, message: any) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
          if (!message || typeof message !== 'object') throw new Error('Invalid message object')
        },
        description: 'Add a new message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'togglePinMessage',
      (id: string) => this.db.togglePinMessage(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Toggle pin status of a message'
      }
    )

    // File endpoints
    this.apiRegistry.registerEndpoint('db', 'getFiles',
      () => this.db.getFiles(),
      { description: 'Get all files' }
    )
    this.apiRegistry.registerEndpoint('db', 'getFile',
      (id: string) => this.db.getFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Get a specific file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addFile',
      (file: any) => this.db.addFile(file),
      {
        validator: (file: any) => {
          if (!file || typeof file !== 'object') throw new Error('Invalid file object')
        },
        description: 'Add a new file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateFile',
      (id: string, updates: any) => this.db.updateFile(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update a file'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'deleteFile',
      (id: string) => this.db.deleteFile(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Delete a file'
      }
    )

    // Artifact endpoints
    this.apiRegistry.registerEndpoint('db', 'getArtifacts',
      (messageId: string) => this.db.getArtifacts(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get artifacts for a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addArtifact',
      (messageId: string, artifact: any) => this.db.addArtifact(messageId, artifact),
      {
        validator: (messageId: string, artifact: any) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!artifact || typeof artifact !== 'object') throw new Error('Invalid artifact object')
        },
        description: 'Add an artifact to a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'updateArtifact',
      (id: string, updates: any) => this.db.updateArtifact(id, updates),
      {
        validator: (id: string, updates: any) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
          if (!updates || typeof updates !== 'object') throw new Error('Invalid updates object')
        },
        description: 'Update an artifact'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'removeArtifact',
      (id: string) => this.db.removeArtifact(id),
      {
        validator: (id: string) => {
          if (!this.validateInput(id, 'string', 100)) throw new Error('Invalid artifact ID')
        },
        description: 'Remove an artifact'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversationArtifacts',
      (conversationId: string) => this.db.getConversationArtifacts(conversationId),
      {
        validator: (conversationId: string) => {
          if (!this.validateInput(conversationId, 'string', 100)) throw new Error('Invalid conversation ID')
        },
        description: 'Get all artifacts for a conversation'
      }
    )

    // Intelligence endpoints
    this.apiRegistry.registerEndpoint('db', 'updateMessageIntelligence',
      (messageId: string, entities: string, topics: string, confidence: number) =>
        this.db.updateMessageIntelligence(messageId, entities, topics, confidence),
      {
        validator: (messageId: string, entities: string, topics: string, confidence: number) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(entities, 'string', 5000)) throw new Error('Invalid entities')
          if (!this.validateInput(topics, 'string', 5000)) throw new Error('Invalid topics')
          if (typeof confidence !== 'number' || confidence < 0 || confidence > 1) throw new Error('Invalid confidence')
        },
        description: 'Update message intelligence data'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'addPinnedIntelligence',
      (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) =>
        this.db.addPinnedIntelligence(messageId, extractionData, vaultAssignment, processingMetadata),
      {
        validator: (messageId: string, extractionData: string, vaultAssignment: string, processingMetadata: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(extractionData, 'string', 10000)) throw new Error('Invalid extraction data')
          if (!this.validateInput(vaultAssignment, 'string', 1000)) throw new Error('Invalid vault assignment')
          if (!this.validateInput(processingMetadata, 'string', 5000)) throw new Error('Invalid processing metadata')
        },
        description: 'Add pinned intelligence data'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getPinnedIntelligence',
      (messageId: string) => this.db.getPinnedIntelligence(messageId),
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get pinned intelligence for a message'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getAllPinnedIntelligence',
      () => this.db.getAllPinnedIntelligence(),
      { description: 'Get all pinned intelligence data' }
    )
    this.apiRegistry.registerEndpoint('db', 'searchConversations',
      (searchTerm: string) => this.db.searchConversationsAndMessages(searchTerm),
      {
        validator: (searchTerm: string) => {
          if (!this.validateInput(searchTerm, 'string', 200)) throw new Error('Invalid search term')
        },
        description: 'Search conversations and messages'
      }
    )
    this.apiRegistry.registerEndpoint('db', 'getConversationsWithArtifacts',
      () => this.db.getConversationsWithArtifacts(),
      { description: 'Get conversations that have artifacts' }
    )

    // Database diagnostics
    this.apiRegistry.registerEndpoint('db', 'getDatabaseHealth',
      () => this.db.getDatabaseHealth(),
      { description: 'Get database health information' }
    )


    this.apiRegistry.registerEndpoint('db', 'createBackup',
      () => this.db.createBackup(),
      { description: 'Create a database backup' }
    )

    // Settings APIs
    this.apiRegistry.registerCategory('settings')
    this.apiRegistry.registerEndpoint('settings', 'get',
      (key: string) => this.db.getSetting(key),
      {
        validator: (key: string) => {
          if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
        },
        description: 'Get a setting value'
      }
    )
    this.apiRegistry.registerEndpoint('settings', 'set',
      (key: string, value: any) => this.db.setSetting(key, value),
      {
        validator: (key: string, value: any) => {
          if (!this.validateInput(key, 'string', 50)) throw new Error('Invalid settings key')
          if (value === undefined || value === null) throw new Error('Invalid setting value')
        },
        description: 'Set a setting value'
      }
    )

    // Vault APIs
    console.log('[MAIN] Registering vault APIs...')
    this.apiRegistry.registerCategory('vault')
    console.log('[MAIN] Vault category registered')

    this.apiRegistry.registerEndpoint('vault', 'createDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.mkdir(dirPath, { recursive: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Create directory recursively'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'writeFile',
      async (filePath: string, content: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Ensure directory exists
          const dir = path.dirname(filePath)
          await fs.promises.mkdir(dir, { recursive: true })

          await fs.promises.writeFile(filePath, content, 'utf8')
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, content: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(content, 'string', 10000000)) throw new Error('Invalid content')
        },
        description: 'Write content to file'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'writeFileBuffer',
      async (filePath: string, buffer: any) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          console.log('writeFileBuffer called with:', {
            filePath,
            bufferType: typeof buffer,
            bufferConstructor: buffer?.constructor?.name,
            isUint8Array: buffer instanceof Uint8Array,
            isArrayBuffer: buffer instanceof ArrayBuffer,
            isArray: Array.isArray(buffer),
            bufferLength: buffer?.length
          });

          // Ensure directory exists
          const dir = path.dirname(filePath)
          await fs.promises.mkdir(dir, { recursive: true })

          let finalBuffer: Buffer;
          if (buffer instanceof Uint8Array) {
            finalBuffer = Buffer.from(buffer);
          } else if (buffer && buffer.type === 'Buffer' && Array.isArray(buffer.data)) {
            // This handles buffers that have been serialized over IPC
            finalBuffer = Buffer.from(buffer.data);
          } else {
            // If it's not a recognized format, throw an error
            throw new Error('Unsupported buffer format provided to writeFileBuffer.');
          }

          await fs.promises.writeFile(filePath, finalBuffer)
          return { success: true }
        } catch (error: any) {
          console.error('writeFileBuffer error:', error);
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, buffer: any) => {
          console.log('writeFileBuffer validator called with:', {
            filePath,
            bufferType: typeof buffer,
            bufferConstructor: buffer?.constructor?.name,
            isUint8Array: buffer instanceof Uint8Array,
            isArrayBuffer: buffer instanceof ArrayBuffer,
            isArray: Array.isArray(buffer),
            hasLength: typeof buffer?.length === 'number',
            bufferLength: buffer?.length
          });

          if (!this.validateInput(filePath, 'string', 500)) {
            console.log('Invalid file path validation failed');
            throw new Error('Invalid file path')
          }

          // Check if buffer is a valid buffer-like object (handles IPC serialization)
          if (!buffer ||
              (!(buffer instanceof Uint8Array) &&
               !(buffer instanceof ArrayBuffer) &&
               !Array.isArray(buffer) &&
               (typeof buffer !== 'object' || typeof buffer.length !== 'number'))) {
            console.log('Invalid buffer validation failed');
            throw new Error('Invalid buffer')
          }

          console.log('writeFileBuffer validator passed');
        },
        description: 'Write binary content to file'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'readDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const items = await fs.promises.readdir(dirPath, { withFileTypes: true })

          const result = await Promise.all(items.map(async (item) => {
            const itemPath = path.join(dirPath, item.name)
            const stats = await fs.promises.stat(itemPath)

            return {
              name: item.name,
              path: itemPath,
              isDirectory: item.isDirectory(),
              size: item.isFile() ? stats.size : undefined,
              modified: stats.mtime.toISOString()
            }
          }))

          return { success: true, items: result }
        } catch (error: any) {
          return { success: false, error: error.message, items: [] }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Read directory contents'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'removeDirectory',
      async (dirPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.rm(dirPath, { recursive: true, force: true })
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (dirPath: string) => {
          if (!this.validateInput(dirPath, 'string', 500)) throw new Error('Invalid directory path')
        },
        description: 'Remove directory recursively'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'removeFile',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.unlink(filePath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Remove file'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'scanFolder',
      async (folderPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Check if folder exists
          const stats = await fs.promises.stat(folderPath)
          if (!stats.isDirectory()) {
            throw new Error('Path is not a directory')
          }

          // Read directory contents
          const entries = await fs.promises.readdir(folderPath, { withFileTypes: true })

          const files = await Promise.all(entries.map(async (entry) => {
            const fullPath = path.join(folderPath, entry.name)
            let fileStats

            try {
              fileStats = await fs.promises.stat(fullPath)
            } catch (error) {
              // Skip files that can't be accessed
              return null
            }

            return {
              name: entry.name,
              path: fullPath,
              isDirectory: entry.isDirectory(),
              size: entry.isDirectory() ? 0 : fileStats.size,
              lastModified: fileStats.mtime.toISOString()
            }
          }))

          // Filter out null entries (files that couldn't be accessed)
          const validFiles = files.filter(file => file !== null)

          return { success: true, files: validFiles }
        } catch (error: any) {
          return { success: false, error: error.message, files: [] }
        }
      },
      {
        validator: (folderPath: string) => {
          if (!this.validateInput(folderPath, 'string', 500)) throw new Error('Invalid folder path')
        },
        description: 'Scan folder for files'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'copyFile',
      async (sourcePath: string, destinationPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Ensure destination directory exists
          const destDir = path.dirname(destinationPath)
          await fs.promises.mkdir(destDir, { recursive: true })

          // Copy the file (handles binary files properly)
          await fs.promises.copyFile(sourcePath, destinationPath)

          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (sourcePath: string, destinationPath: string) => {
          if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
          if (!this.validateInput(destinationPath, 'string', 500)) throw new Error('Invalid destination path')
        },
        description: 'Copy file from source to destination'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'pathExists',
      async (targetPath: string) => {
        const fs = await import('fs')
        try {
          await fs.promises.access(targetPath)
          return { exists: true }
        } catch (error: any) {
          return { exists: false, error: error.message }
        }
      },
      {
        validator: (targetPath: string) => {
          if (!this.validateInput(targetPath, 'string', 500)) throw new Error('Invalid path')
        },
        description: 'Check if path exists'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'readFile',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          const content = await fs.promises.readFile(filePath, 'utf8')
          return { success: true, content }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Read file content'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'getVaultRegistry',
      async () => {
        const fs = await import('fs')
        const path = await import('path')
        const os = await import('os')
        try {
          // First try to get saved vault root path from database
          let vaultRoot = this.db.getSetting('vault-root-path')

          // Fall back to default if no saved path
          if (!vaultRoot) {
            vaultRoot = path.join(os.homedir(), 'Documents', 'ChatLo_Vaults')
          }

          const registryPath = path.join(vaultRoot, '.chatlo', 'vault-registry.json')

          if (!await fs.promises.access(registryPath).then(() => true).catch(() => false)) {
            return null
          }

          const content = await fs.promises.readFile(registryPath, 'utf8')
          const registry = JSON.parse(content)
          return registry
        } catch (error: any) {
          console.error('Error getting vault registry:', error)
          return null
        }
      },
      { description: 'Get vault registry configuration' }
    )

    this.apiRegistry.registerEndpoint('vault', 'saveVaultRegistry',
      async (registry: any) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const registryPath = path.join(registry.vaultRoot, '.chatlo', 'vault-registry.json')
          const registryDir = path.dirname(registryPath)

          // Ensure directory exists
          await fs.promises.mkdir(registryDir, { recursive: true })

          // Save registry
          await fs.promises.writeFile(registryPath, JSON.stringify(registry, null, 2), 'utf8')
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (registry: any) => {
          if (!registry || typeof registry !== 'object') throw new Error('Invalid registry object')
        },
        description: 'Save vault registry configuration'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'initializeVaultRoot',
      async (rootPath: string, template: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          // Create root directory if it doesn't exist
          await fs.promises.mkdir(rootPath, { recursive: true })

          // Create basic vault structure based on template
          const vaults: any[] = []

          if (template === 'default') {
            // Create Personal and Work vaults
            const personalPath = path.join(rootPath, 'personal-vault')
            const workPath = path.join(rootPath, 'work-vault')

            await fs.promises.mkdir(personalPath, { recursive: true })
            await fs.promises.mkdir(workPath, { recursive: true })

            // Create getting-started context in personal vault
            const gettingStartedPath = path.join(personalPath, 'getting-started')
            await this.createContextStructure(gettingStartedPath, 'Your First Context Vault')

            // Create projects context in work vault
            const projectsPath = path.join(workPath, 'projects')
            await this.createContextStructure(projectsPath, 'Projects')

            vaults.push(
              { id: 'personal-vault', name: 'Personal Vault', path: personalPath },
              { id: 'work-vault', name: 'Work Vault', path: workPath }
            )
          } else if (template === 'simple') {
            // Create single vault
            const vaultPath = path.join(rootPath, 'my-vault')
            await fs.promises.mkdir(vaultPath, { recursive: true })

            // Create getting-started context
            const gettingStartedPath = path.join(vaultPath, 'getting-started')
            await this.createContextStructure(gettingStartedPath, 'Getting Started')

            vaults.push({ id: 'my-vault', name: 'My Vault', path: vaultPath })
          }

          return { success: true, vaults }
        } catch (error: any) {
          return { success: false, vaults: [], error: error.message }
        }
      },
      {
        validator: (rootPath: string, template: string) => {
          if (!this.validateInput(rootPath, 'string', 500)) throw new Error('Invalid root path')
          if (!this.validateInput(template, 'string', 50)) throw new Error('Invalid template')
        },
        description: 'Initialize vault root with template'
      }
    )

    this.apiRegistry.registerEndpoint('vault', 'scanContexts',
      async (vaultPath: string) => {
        const fs = await import('fs')
        const path = await import('path')
        try {
          const items = await fs.promises.readdir(vaultPath, { withFileTypes: true })
          const contexts: any[] = []

          for (const item of items) {
            if (item.isDirectory() && !item.name.startsWith('.')) {
              const contextPath = path.join(vaultPath, item.name)
              const masterPath = path.join(contextPath, 'master.md')

              // Check if master.md exists
              const masterExists = await fs.promises.access(masterPath).then(() => true).catch(() => false)

              if (masterExists) {
                contexts.push({
                  id: item.name,
                  name: item.name,
                  path: contextPath,
                  hasMaster: true
                })
              }
            }
          }

          return { success: true, contexts }
        } catch (error: any) {
          return { success: false, contexts: [], error: error.message }
        }
      },
      {
        validator: (vaultPath: string) => {
          if (!this.validateInput(vaultPath, 'string', 500)) throw new Error('Invalid vault path')
        },
        description: 'Scan vault for contexts'
      }
    )

    console.log('[MAIN] All vault APIs registered successfully')

    // File System APIs
    console.log('[MAIN] Registering file system APIs...')
    this.apiRegistry.registerCategory('files')

    this.apiRegistry.registerEndpoint('files', 'getChatloFolderPath',
      () => {
        return this.fileSystem.getChatloFolderPath()
      },
      { description: 'Get the current ChatLo folder path' }
    )

    this.apiRegistry.registerEndpoint('files', 'setChatloFolderPath',
      async (newPath: string) => {
        try {
          await this.fileSystem.setChatloFolderPath(newPath)
          return { success: true }
        } catch (error: any) {
          return { success: false, error: error.message }
        }
      },
      {
        validator: (newPath: string) => {
          if (!this.validateInput(newPath, 'string', 500)) throw new Error('Invalid folder path')
        },
        description: 'Set a new ChatLo folder path'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'getIndexedFiles',
      async () => {
        try {
          const files = await this.fileSystem.getIndexedFiles()
          return files
        } catch (error: any) {
          console.error('Error getting indexed files:', error)
          return []
        }
      },
      { description: 'Get all indexed files' }
    )

    this.apiRegistry.registerEndpoint('files', 'searchFiles',
      async (query: string, limit?: number) => {
        try {
          const results = await this.fileSystem.searchFiles(query, limit)
          return results
        } catch (error: any) {
          console.error('Error searching files:', error)
          return []
        }
      },
      {
        validator: (query: string, limit?: number) => {
          if (!this.validateInput(query, 'string', 200)) throw new Error('Invalid search query')
          if (limit !== undefined && (!Number.isInteger(limit) || limit < 1 || limit > 1000)) {
            throw new Error('Invalid limit value')
          }
        },
        description: 'Search files by query'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'processFileContent',
      async (fileId: string) => {
        try {
          const result = await this.fileSystem.processFileContent(fileId)
          return result
        } catch (error: any) {
          console.error('Error processing file content:', error)
          return false
        }
      },
      {
        validator: (fileId: string) => {
          if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Process file content for intelligence'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'indexFile',
      async (filePath: string, processContent?: boolean) => {
        try {
          const result = await this.fileSystem.indexFile(filePath, processContent)
          return result
        } catch (error: any) {
          console.error('Error indexing file:', error)
          return null
        }
      },
      {
        validator: (filePath: string, processContent?: boolean) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Index a single file'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'indexVaultFile',
      async (filePath: string, vaultName: string, relativePath: string, processContent?: boolean) => {
        try {
          const result = await this.fileSystem.indexVaultFile(filePath, vaultName, relativePath, processContent)
          return result
        } catch (error: any) {
          console.error('Error indexing vault file:', error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (filePath: string, vaultName: string, relativePath: string, processContent?: boolean) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
          if (!this.validateInput(vaultName, 'string', 100)) throw new Error('Invalid vault name')
          if (!this.validateInput(relativePath, 'string', 500)) throw new Error('Invalid relative path')
        },
        description: 'Index a vault file with vault-specific metadata'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'indexAllFiles',
      async () => {
        try {
          await this.fileSystem.indexAllFiles()
          return { success: true }
        } catch (error: any) {
          console.error('Error indexing all files:', error)
          return { success: false, error: error.message }
        }
      },
      { description: 'Index all files in ChatLo folder' }
    )

    this.apiRegistry.registerEndpoint('files', 'copyFileToUploads',
      async (sourcePath: string, filename?: string) => {
        try {
          const result = await this.fileSystem.copyFileToUploads(sourcePath, filename)
          return result
        } catch (error: any) {
          console.error('Error copying file to uploads:', error)
          throw error
        }
      },
      {
        validator: (sourcePath: string, filename?: string) => {
          if (!this.validateInput(sourcePath, 'string', 500)) throw new Error('Invalid source path')
          if (filename && !this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
        },
        description: 'Copy file to uploads directory'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'saveContentAsFile',
      async (content: string, filename: string, subfolder?: string) => {
        try {
          const result = await this.fileSystem.saveContentAsFile(content, filename, subfolder)
          return result
        } catch (error: any) {
          console.error('Error saving content as file:', error)
          throw error
        }
      },
      {
        validator: (content: string, filename: string, subfolder?: string) => {
          if (!this.validateInput(content, 'string', 10000000)) throw new Error('Invalid content')
          if (!this.validateInput(filename, 'string', 255)) throw new Error('Invalid filename')
          if (subfolder && !this.validateInput(subfolder, 'string', 100)) throw new Error('Invalid subfolder')
        },
        description: 'Save content as a file'
      }
    )

    // File attachment operations
    this.apiRegistry.registerEndpoint('files', 'addFileAttachment',
      async (messageId: string, fileId: string, attachmentType: 'attachment' | 'reference') => {
        try {
          const result = await this.db.addFileAttachment(messageId, fileId, attachmentType)
          return result
        } catch (error: any) {
          console.error('Error adding file attachment:', error)
          throw error
        }
      },
      {
        validator: (messageId: string, fileId: string, attachmentType: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
          if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
          if (!['attachment', 'reference'].includes(attachmentType)) throw new Error('Invalid attachment type')
        },
        description: 'Add file attachment to message'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'getFileAttachments',
      async (messageId: string) => {
        try {
          const result = await this.db.getFileAttachments(messageId)
          return result
        } catch (error: any) {
          console.error('Error getting file attachments:', error)
          return []
        }
      },
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get file attachments for message'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'getMessageFiles',
      async (messageId: string) => {
        try {
          const result = await this.db.getMessageFiles(messageId)
          return result
        } catch (error: any) {
          console.error('Error getting message files:', error)
          return []
        }
      },
      {
        validator: (messageId: string) => {
          if (!this.validateInput(messageId, 'string', 100)) throw new Error('Invalid message ID')
        },
        description: 'Get files associated with message'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'removeFileAttachment',
      async (attachmentId: string) => {
        try {
          await this.db.removeFileAttachment(attachmentId)
          return { success: true }
        } catch (error: any) {
          console.error('Error removing file attachment:', error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (attachmentId: string) => {
          if (!this.validateInput(attachmentId, 'string', 100)) throw new Error('Invalid attachment ID')
        },
        description: 'Remove file attachment'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'deleteFile',
      async (fileId: string) => {
        try {
          const result = await this.fileSystem.deleteFile(fileId)
          return result
        } catch (error: any) {
          console.error('Error deleting file:', error)
          return false
        }
      },
      {
        validator: (fileId: string) => {
          if (!this.validateInput(fileId, 'string', 100)) throw new Error('Invalid file ID')
        },
        description: 'Delete a file'
      }
    )

    // File dialog APIs
    this.apiRegistry.registerEndpoint('files', 'showOpenDialog',
      async (options: any) => {
        try {
          const { dialog } = require('electron')
          const result = await dialog.showOpenDialog(options)
          return result
        } catch (error: any) {
          console.error('Error showing open dialog:', error)
          throw error
        }
      },
      {
        validator: (options: any) => {
          if (!options || typeof options !== 'object') throw new Error('Invalid dialog options')
        },
        description: 'Show open file dialog'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'showSaveDialog',
      async (options: any) => {
        try {
          const { dialog } = require('electron')
          const result = await dialog.showSaveDialog(options)
          return result
        } catch (error: any) {
          console.error('Error showing save dialog:', error)
          throw error
        }
      },
      {
        validator: (options: any) => {
          if (!options || typeof options !== 'object') throw new Error('Invalid dialog options')
        },
        description: 'Show save file dialog'
      }
    )

    // Enhanced file operations for 2-step path translation
    this.apiRegistry.registerEndpoint('files', 'getFileStats',
      async (filePath: string) => {
        const fs = await import('fs')
        try {
          const stats = await fs.promises.stat(filePath)
          return {
            size: stats.size,
            mtime: stats.mtime,
            ctime: stats.ctime,
            isFile: stats.isFile(),
            isDirectory: stats.isDirectory()
          }
        } catch (error: any) {
          throw new Error(`Failed to get file stats: ${error.message}`)
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Get file statistics'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'calculateFileHash',
      async (filePath: string) => {
        try {
          const hash = this.fileSystem.calculateFileHash(filePath)
          return hash
        } catch (error: any) {
          throw new Error(`Failed to calculate file hash: ${error.message}`)
        }
      },
      {
        validator: (filePath: string) => {
          if (!this.validateInput(filePath, 'string', 500)) throw new Error('Invalid file path')
        },
        description: 'Calculate SHA256 hash of file'
      }
    )

    this.apiRegistry.registerEndpoint('files', 'getPlatformInfo',
      async () => {
        const os = await import('os')
        const path = await import('path')
        return {
          platform: process.platform,
          separator: path.sep,
          homeDir: os.homedir(),
          documentsDir: path.join(os.homedir(), 'Documents')
        }
      },
      { description: 'Get platform-specific information' }
    )

    this.apiRegistry.registerEndpoint('files', 'createTempFileFromBuffer',
      async (fileData: { fileName: string; buffer: Uint8Array; mimeType: string; lastModified: number }) => {
        const fs = await import('fs')
        const path = await import('path')
        const os = await import('os')

        try {
          // Create a temporary directory for dropped files
          const tempDir = path.join(os.tmpdir(), 'chatlo-dropped-files')
          await fs.promises.mkdir(tempDir, { recursive: true })

          // Generate a unique temporary file name
          const timestamp = Date.now()
          const randomId = Math.random().toString(36).substr(2, 9)
          const sanitizedFileName = fileData.fileName.replace(/[^a-zA-Z0-9.-]/g, '_')
          const tempFileName = `${timestamp}_${randomId}_${sanitizedFileName}`
          const tempPath = path.join(tempDir, tempFileName)

          // Write the buffer to the temporary file
          // Handle serialized Uint8Array from IPC: { '0': 1, '1': 2, ... }
          let buffer: Buffer
          if (fileData.buffer instanceof Uint8Array) {
            // Direct Uint8Array (shouldn't happen over IPC but handle it)
            buffer = Buffer.from(fileData.buffer)
          } else if (Array.isArray(fileData.buffer)) {
            // Array format
            buffer = Buffer.from(fileData.buffer)
          } else {
            // Serialized object format: { '0': 1, '1': 2, ... }
            const keys = Object.keys(fileData.buffer).sort((a, b) => parseInt(a) - parseInt(b))
            const values = keys.map(key => fileData.buffer[key])
            buffer = Buffer.from(values)
          }

          await fs.promises.writeFile(tempPath, buffer)

          // Set the file modification time to match the original
          if (fileData.lastModified) {
            const modTime = new Date(fileData.lastModified)
            await fs.promises.utimes(tempPath, modTime, modTime)
          }

          return { success: true, tempPath }
        } catch (error: any) {
          console.error('Error creating temporary file:', error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (fileData: any) => {
          if (!fileData || typeof fileData !== 'object') throw new Error('Invalid file data')
          if (!this.validateInput(fileData.fileName, 'string', 255)) throw new Error('Invalid file name')

          // Handle serialized Uint8Array from IPC
          if (!fileData.buffer) throw new Error('Missing buffer data')

          // Check if it's a valid buffer format
          // Uint8Array gets serialized to an object with numeric keys: { '0': 1, '1': 2, ... }
          if (typeof fileData.buffer !== 'object') {
            throw new Error('Invalid buffer data - must be object')
          }

          // For serialized Uint8Array, check if it has numeric keys
          const keys = Object.keys(fileData.buffer)
          const hasNumericKeys = keys.length > 0 && keys.every(key => /^\d+$/.test(key))
          const hasLength = 'length' in fileData.buffer || keys.length > 0

          if (!hasNumericKeys && !hasLength) {
            throw new Error('Invalid buffer data - no numeric keys or length')
          }

          // Calculate size from keys if no length property
          const bufferSize = fileData.buffer.length || keys.length
          if (bufferSize > 100 * 1024 * 1024) throw new Error('File too large (max 100MB)')
        },
        description: 'Create temporary file from buffer data for drag-and-drop operations'
      }
    )

    // TODO: Removed diagnostic endpoint registration - module marked for removal

    console.log('[MAIN] All file system APIs registered successfully')

    // Plugin management APIs
    console.log('[MAIN] Registering plugin management APIs...')
    this.apiRegistry.registerCategory('plugins')

    this.apiRegistry.registerEndpoint('plugins', 'getAll',
      async () => {
        try {
          return this.pluginManager.getAllPluginsInfo()
        } catch (error: any) {
          console.error('Error getting all plugins:', error)
          return []
        }
      },
      { description: 'Get all plugins information' }
    )

    this.apiRegistry.registerEndpoint('plugins', 'enable',
      async (pluginId: string, enabled: boolean) => {
        try {
          this.pluginManager.setPluginEnabled(pluginId, enabled)
          return { success: true }
        } catch (error: any) {
          console.error(`Error ${enabled ? 'enabling' : 'disabling'} plugin ${pluginId}:`, error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginId: string, enabled: boolean) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
          if (typeof enabled !== 'boolean') throw new Error('Invalid enabled value')
        },
        description: 'Enable or disable a plugin'
      }
    )

    this.apiRegistry.registerEndpoint('plugins', 'disable',
      async (pluginId: string) => {
        try {
          this.pluginManager.setPluginEnabled(pluginId, false)
          return { success: true }
        } catch (error: any) {
          console.error(`Error disabling plugin ${pluginId}:`, error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Disable a plugin'
      }
    )

    this.apiRegistry.registerEndpoint('plugins', 'discover',
      async () => {
        try {
          const manifests = await this.pluginManager.discoverPlugins()
          return { success: true, manifests }
        } catch (error: any) {
          console.error('Error discovering plugins:', error)
          return { success: false, error: error.message, manifests: [] }
        }
      },
      { description: 'Discover available plugins' }
    )

    this.apiRegistry.registerEndpoint('plugins', 'getConfig',
      async (pluginId: string) => {
        try {
          const config = this.pluginManager.getPluginConfig(pluginId)
          if (config === undefined) {
            return { success: false, error: 'Plugin not found or no config available' }
          }
          return { success: true, config }
        } catch (error: any) {
          console.error(`Error getting config for plugin ${pluginId}:`, error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Get plugin configuration'
      }
    )

    this.apiRegistry.registerEndpoint('plugins', 'updateConfig',
      async (pluginId: string, config: any) => {
        try {
          this.pluginManager.updatePluginConfig(pluginId, config)
          return { success: true }
        } catch (error: any) {
          console.error(`Error updating config for plugin ${pluginId}:`, error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginId: string, config: any) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
          if (!config || typeof config !== 'object') throw new Error('Invalid config object')
        },
        description: 'Update plugin configuration'
      }
    )

    this.apiRegistry.registerEndpoint('plugins', 'getCapabilities',
      async () => {
        try {
          // Return all available plugin capabilities
          const capabilities = Object.values(require('./plugins/types').PluginCapability)
          return { success: true, capabilities }
        } catch (error: any) {
          console.error('Error getting plugin capabilities:', error)
          return { success: false, error: error.message, capabilities: [] }
        }
      },
      { description: 'Get all available plugin capabilities' }
    )

    this.apiRegistry.registerEndpoint('plugins', 'getAPIEndpoints',
      async (pluginId: string) => {
        try {
          const namespace = this.pluginManager.getPluginNamespace(pluginId)
          if (!namespace) {
            return { success: false, error: 'Plugin not found or has no API endpoints' }
          }

          return {
            success: true,
            namespace: namespace.namespace,
            endpoints: namespace.endpoints.map(ep => ({
              name: ep.name,
              description: ep.description,
              hasValidator: !!ep.validator,
              hasMiddleware: !!ep.middleware && ep.middleware.length > 0
            }))
          }
        } catch (error: any) {
          console.error(`Error getting API endpoints for plugin ${pluginId}:`, error)
          return { success: false, error: error.message }
        }
      },
      {
        validator: (pluginId: string) => {
          if (!this.validateInput(pluginId, 'string', 100)) throw new Error('Invalid plugin ID')
        },
        description: 'Get API endpoints for a specific plugin'
      }
    )

    this.apiRegistry.registerEndpoint('plugins', 'getAllAPIEndpoints',
      async () => {
        try {
          const allNamespaces = this.pluginManager.getAllPluginNamespaces()
          const apiInfo = allNamespaces.map(namespace => ({
            pluginId: namespace.pluginId,
            namespace: namespace.namespace,
            endpoints: namespace.endpoints.map(ep => ({
              name: ep.name,
              description: ep.description,
              hasValidator: !!ep.validator,
              hasMiddleware: !!ep.middleware && ep.middleware.length > 0
            }))
          }))

          return { success: true, apiInfo }
        } catch (error: any) {
          console.error('Error getting all plugin API endpoints:', error)
          return { success: false, error: error.message, apiInfo: [] }
        }
      },
      { description: 'Get all plugin API endpoints' }
    )

    console.log('[MAIN] All plugin management APIs registered successfully')

    // Updater APIs
    console.log('[MAIN] Registering updater APIs...')
    this.apiRegistry.registerCategory('updater')

    this.apiRegistry.registerEndpoint('updater', 'check-for-updates',
      async () => {
        try {
          if (isDev) {
            return {
              available: false,
              message: 'Auto-updater is disabled in development mode'
            }
          }

          // Trigger update check
          const result = await autoUpdater.checkForUpdates()

          if (result) {
            return {
              available: true,
              message: `Update available: ${result.updateInfo.version}`
            }
          } else {
            return {
              available: false,
              message: 'No updates available'
            }
          }
        } catch (error: any) {
          console.error('Error checking for updates:', error)
          return {
            available: false,
            error: error.message
          }
        }
      },
      { description: 'Check for application updates' }
    )

    this.apiRegistry.registerEndpoint('updater', 'download-and-install',
      async () => {
        try {
          if (isDev) {
            return {
              success: false,
              message: 'Auto-updater is disabled in development mode'
            }
          }

          // Download and install update
          await autoUpdater.downloadUpdate()

          // The update will be installed when the app is restarted
          // The 'update-downloaded' event will be fired when download completes
          return {
            success: true,
            message: 'Update download started. The app will restart when download completes.'
          }
        } catch (error: any) {
          console.error('Error downloading update:', error)
          return {
            success: false,
            error: error.message
          }
        }
      },
      { description: 'Download and install application update' }
    )

    console.log('[MAIN] All updater APIs registered successfully')

    // System monitoring APIs
    console.log('[MAIN] Registering system monitoring APIs...')
    this.apiRegistry.registerCategory('system')

    this.apiRegistry.registerEndpoint('system', 'getAPIRegistry',
      async () => {
        return { success: true, registry: this.apiRegistry.getRegistryInfo() }
      },
      { description: 'Get API registry information for debugging' }
    )

    this.apiRegistry.registerEndpoint('system', 'getPerformanceMetrics',
      async () => {
        return { success: true, metrics: this.apiRegistry.getPerformanceMetrics() }
      },
      { description: 'Get API performance metrics from middleware' }
    )

    this.apiRegistry.registerEndpoint('system', 'cleanupMiddleware',
      async () => {
        this.apiRegistry.cleanup()
        return { success: true, message: 'Middleware cleanup completed' }
      },
      { description: 'Cleanup middleware resources (rate limiting cache, etc.)' }
    )

    this.apiRegistry.registerEndpoint('system', 'getMonitoringData',
      async () => {
        return { success: true, data: this.apiRegistry.getMonitoringData() }
      },
      { description: 'Get comprehensive monitoring data including metrics, health, and alerts' }
    )

    this.apiRegistry.registerEndpoint('system', 'getEndpointMetrics',
      async (category: string, endpoint: string) => {
        const metrics = this.apiRegistry.getEndpointMetrics(category, endpoint)
        return { success: true, metrics }
      },
      { description: 'Get detailed metrics for a specific API endpoint' }
    )

    this.apiRegistry.registerEndpoint('system', 'resetMonitoring',
      async () => {
        this.apiRegistry.resetMonitoring()
        return { success: true, message: 'Monitoring data reset successfully' }
      },
      { description: 'Reset all monitoring data and metrics' }
    )
    this.apiRegistry.registerEndpoint('system', 'getErrorStatistics',
      async () => {
        const stats = this.apiRegistry.getErrorStatistics()
        return { success: true, data: stats }
      },
      { description: 'Get error statistics and history' }
    )
    this.apiRegistry.registerEndpoint('system', 'clearErrorHistory',
      async () => {
        this.apiRegistry.clearErrorHistory()
        return { success: true, message: 'Error history cleared successfully' }
      },
      { description: 'Clear error history and statistics' }
    )

    console.log('[MAIN] All system monitoring APIs registered successfully')

    console.log('[MAIN] registerCoreAPIs completed successfully')
    } catch (error) {
      console.error('[MAIN] Error in registerCoreAPIs:', error)
      throw error
    }
  }

  // TODO: Removed diagnoseFileProcessing method - diagnostic module marked for removal

  // Initialize plugins
  private async initializePlugins(): Promise<void> {
    try {
      // Discover plugins
      const manifests = await this.pluginManager.discoverPlugins()
      console.log(`Discovered ${manifests.length} plugins`)
      
      // Load core plugins first
      const corePlugins = manifests.filter(m => m.id.startsWith('core-'))
      for (const manifest of corePlugins) {
        try {
          await this.pluginManager.loadPlugin(manifest)
        } catch (error) {
          console.error(`Failed to load core plugin ${manifest.id}:`, error)
        }
      }
      
      // Load optional plugins
      const optionalPlugins = manifests.filter(m => !m.id.startsWith('core-'))
      for (const manifest of optionalPlugins) {
        try {
          await this.pluginManager.loadPlugin(manifest)
        } catch (error) {
          console.warn(`Failed to load optional plugin ${manifest.id}:`, error)
        }
      }
      
    } catch (error) {
      console.error('Error initializing plugins:', error)
    }
  }

  // Initialize API server
  private async initializeApiServer(): Promise<void> {
    try {
      console.log('[ELECTRON] Starting FileRoutingService API Server...')

      this.apiServer = await startApiServer({
        port: 3001,
        host: 'localhost',
        corsOrigins: ['http://localhost:3000', 'http://localhost:5173'],
        rateLimit: {
          windowMs: 15 * 60 * 1000, // 15 minutes
          max: 100 // limit each IP to 100 requests per windowMs
        },
        upload: {
          maxFileSize: 50 * 1024 * 1024, // 50MB
          maxFiles: 10
        }
      })

      console.log('[ELECTRON] FileRoutingService API Server started successfully on http://localhost:3001')
    } catch (error) {
      console.error('[ELECTRON] Failed to start API server:', error)
      // Don't throw - the main app should still work without the API server
    }
  }

  public async init(): Promise<void> {
    await app.whenReady()

    // Check if vault system is configured - required for new architecture
    try {
      const vaultRootPath = this.db.getSetting('vault-root-path')
      console.log('[ELECTRON] Checking for vault system, vault root path:', vaultRootPath)

      if (vaultRootPath) {
        console.log('[ELECTRON] Vault system detected and ready')
      } else {
        console.log('[ELECTRON] No vault system configured - user will need to set up context vault on first run')
      }
    } catch (error) {
      console.error('Error checking vault system:', error)
    }

    this.setupIPC()
    await this.initializePlugins()
    await this.initializeApiServer()
    this.setupAutoUpdater()
    this.createWindow()

    app.on('activate', () => {
      if (BrowserWindow.getAllWindows().length === 0) {
        this.createWindow()
      }
    })

    app.on('window-all-closed', async () => {
      // Clean up API server
      if (this.apiServer) {
        try {
          console.log('[ELECTRON] Stopping API server...')
          await stopApiServer(this.apiServer)
          console.log('[ELECTRON] API server stopped successfully')
        } catch (error) {
          console.error('[ELECTRON] Error stopping API server:', error)
        }
      }

      if (process.platform !== 'darwin') {
        app.quit()
      }
    })
  }
}

const application = new App()
application.init().catch(console.error)

ipcMain.on('window-minimize', () => {
  if (application.mainWindow) application.mainWindow.minimize();
});
ipcMain.on('window-maximize', () => {
  if (application.mainWindow) {
    if (application.mainWindow.isMaximized()) {
      application.mainWindow.unmaximize();
    } else {
      application.mainWindow.maximize();
    }
  }
});
ipcMain.on('window-close', () => {
  if (application.mainWindow) application.mainWindow.close();
});
