/**
 * FileRoutingService API Server
 * HTTP API server that exposes FileRoutingService functionality
 * Implements token-based authentication and comprehensive audit logging
 */

import express, { Request, Response, NextFunction } from 'express'
import multer from 'multer'
import cors from 'cors'
import rateLimit from 'express-rate-limit'
// import { fileRoutingService } from '../../../src/services/fileRoutingService'
import { ServiceAccessLogger } from './ServiceAccessLogger'
import { TokenManager } from './TokenManager'
import { ServiceRegistry } from './ServiceRegistry'
import { BaseService, ServiceError, ServiceErrorCode } from '../../../src/services/base'

export interface ApiServerConfig {
  port: number
  host: string
  corsOrigins: string[]
  rateLimit: {
    windowMs: number
    max: number
  }
  upload: {
    maxFileSize: number
    maxFiles: number
    allowedMimeTypes?: string[]
  }
}

export interface ApiRequest extends Request {
  serviceId?: string
  tokenData?: any
  uploadId?: string
  requestId?: string
}

export interface ApiResponse {
  success: boolean
  data?: any
  error?: {
    code: string
    message: string
    details?: any
  }
  metadata?: {
    requestId: string
    timestamp: string
    processingTime: number
  }
}

class FileRoutingApiServer extends BaseService {
  private app: express.Application
  private server: any
  private config: ApiServerConfig
  private upload: multer.Multer
  private accessLogger: ServiceAccessLogger
  private tokenManager: TokenManager
  private serviceRegistry: ServiceRegistry

  constructor(config: ApiServerConfig) {
    super({
      name: 'FileRoutingApiServer',
      autoInitialize: false
    })

    this.config = config
    this.app = express()
    this.accessLogger = new ServiceAccessLogger()
    this.tokenManager = new TokenManager()
    this.serviceRegistry = new ServiceRegistry()

    // Configure multer for file uploads
    this.upload = multer({
      storage: multer.memoryStorage(),
      limits: {
        fileSize: config.upload.maxFileSize,
        files: config.upload.maxFiles
      },
      fileFilter: (req, file, cb) => {
        if (config.upload.allowedMimeTypes) {
          const isAllowed = config.upload.allowedMimeTypes.some(type => {
            if (type.endsWith('/*')) {
              return file.mimetype.startsWith(type.slice(0, -1))
            }
            return file.mimetype === type
          })
          cb(null, isAllowed)
        } else {
          cb(null, true)
        }
      }
    })
  }

  protected async doInitialize(): Promise<void> {
    // Initialize dependencies
    await this.accessLogger.initialize()
    await this.tokenManager.initialize()
    await this.serviceRegistry.initialize()

    // Setup middleware
    this.setupMiddleware()

    // Setup routes
    this.setupRoutes()

    // Start server
    await this.startServer()

    this.logger.info('FileRoutingService API Server initialized successfully', 'doInitialize', {
      port: this.config.port,
      host: this.config.host
    })
  }

  protected async doCleanup(): Promise<void> {
    if (this.server) {
      await new Promise<void>((resolve) => {
        this.server.close(() => resolve())
      })
    }

    await this.accessLogger.cleanup()
    await this.tokenManager.cleanup()
    await this.serviceRegistry.cleanup()

    this.logger.info('FileRoutingService API Server cleaned up', 'doCleanup')
  }

  private setupMiddleware(): void {
    // CORS
    this.app.use(cors({
      origin: this.config.corsOrigins,
      credentials: true
    }))

    // Rate limiting
    const limiter = rateLimit({
      windowMs: this.config.rateLimit.windowMs,
      max: this.config.rateLimit.max,
      message: {
        success: false,
        error: {
          code: 'RATE_LIMIT_EXCEEDED',
          message: 'Too many requests, please try again later'
        }
      }
    })
    this.app.use('/api/', limiter)

    // Body parsing
    this.app.use(express.json({ limit: '10mb' }))
    this.app.use(express.urlencoded({ extended: true, limit: '10mb' }))

    // Request logging
    this.app.use(this.requestLogger.bind(this))

    // Authentication middleware
    this.app.use('/api/v1/', this.authenticateToken.bind(this))
  }

  private setupRoutes(): void {
    // Health check (no auth required)
    this.app.get('/health', this.handleHealthCheck.bind(this))

    // Service registration (no auth required)
    this.app.post('/api/v1/services/register', this.handleServiceRegistration.bind(this))

    // Token management (API key auth)
    this.app.post('/api/v1/auth/token', this.handleTokenRequest.bind(this))
    this.app.post('/api/v1/auth/refresh', this.handleTokenRefresh.bind(this))

    // File operations (token auth)
    this.app.post('/api/v1/files/upload', this.upload.array('files'), this.handleFileUpload.bind(this))
    this.app.get('/api/v1/files/read', this.handleFileRead.bind(this))
    this.app.get('/api/v1/files/list', this.handleFileList.bind(this))
    this.app.delete('/api/v1/files/delete', this.handleFileDelete.bind(this))

    // Service management
    this.app.get('/api/v1/services/status', this.handleServiceStatus.bind(this))
    this.app.get('/api/v1/services/metrics', this.handleServiceMetrics.bind(this))

    // Error handling
    this.app.use(this.errorHandler.bind(this))
  }

  private async startServer(): Promise<void> {
    return new Promise((resolve, reject) => {
      this.server = this.app.listen(this.config.port, this.config.host, () => {
        this.logger.info(`API Server listening on ${this.config.host}:${this.config.port}`, 'startServer')
        resolve()
      })

      this.server.on('error', (error: Error) => {
        this.logger.error('Server error', 'startServer', error)
        reject(error)
      })
    })
  }

  private async requestLogger(req: Request, res: Response, next: NextFunction): Promise<void> {
    const startTime = Date.now()
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`

    // Add request ID to request
    ;(req as any).requestId = requestId

    // Log request
    this.logger.debug(`${req.method} ${req.path}`, 'requestLogger', {
      requestId,
      ip: req.ip,
      userAgent: req.get('User-Agent')
    })

    // Override res.json to add metadata
    const originalJson = res.json.bind(res)
    res.json = (body: any) => {
      const processingTime = Date.now() - startTime
      
      if (body && typeof body === 'object') {
        body.metadata = {
          requestId,
          timestamp: new Date().toISOString(),
          processingTime
        }
      }

      return originalJson(body)
    }

    next()
  }

  private async authenticateToken(req: ApiRequest, res: Response, next: NextFunction): Promise<void> {
    try {
      // Skip auth for registration and token endpoints
      if (req.path.includes('/services/register') || req.path.includes('/auth/')) {
        return next()
      }

      const authHeader = req.headers.authorization
      if (!authHeader || !authHeader.startsWith('Bearer ')) {
        res.status(401).json({
          success: false,
          error: {
            code: 'MISSING_TOKEN',
            message: 'Authorization token required'
          }
        })
        return
      }

      const token = authHeader.substring(7)
      const tokenData = await this.tokenManager.validateToken(token)

      if (!tokenData) {
        res.status(401).json({
          success: false,
          error: {
            code: 'INVALID_TOKEN',
            message: 'Invalid or expired token'
          }
        })
        return
      }

      req.serviceId = tokenData.serviceId
      req.tokenData = tokenData

      // Log access attempt
      await this.accessLogger.logAccess({
        serviceId: tokenData.serviceId,
        endpoint: req.path,
        method: req.method,
        success: true,
        ip: req.ip,
        userAgent: req.get('User-Agent')
      })

      next()
    } catch (error) {
      this.logger.error('Authentication error', 'authenticateToken', error)
      res.status(500).json({
        success: false,
        error: {
          code: 'AUTH_ERROR',
          message: 'Authentication failed'
        }
      })
    }
  }

  private async handleHealthCheck(req: Request, res: Response): Promise<void> {
    // const health = await fileRoutingService.healthCheck()
    const health = true // TODO: Implement proper health check
    res.json({
      success: true,
      data: {
        status: health ? 'healthy' : 'unhealthy',
        timestamp: new Date().toISOString(),
        version: '1.0.0'
      }
    })
  }

  private async handleServiceRegistration(req: Request, res: Response): Promise<void> {
    try {
      const registration = await this.serviceRegistry.registerService(req.body)
      res.json({
        success: true,
        data: registration
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleTokenRequest(req: Request, res: Response): Promise<void> {
    try {
      const token = await this.tokenManager.generateToken(req.body)
      res.json({
        success: true,
        data: token
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleTokenRefresh(req: Request, res: Response): Promise<void> {
    try {
      const token = await this.tokenManager.refreshToken(req.body.refreshToken)
      res.json({
        success: true,
        data: token
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleFileUpload(req: ApiRequest, res: Response): Promise<void> {
    try {
      if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        res.status(400).json({
          success: false,
          error: {
            code: 'NO_FILES',
            message: 'No files provided'
          }
        })
        return
      }

      // Parse destination and options
      const destination = JSON.parse(req.body.destination || '{}')
      const options = JSON.parse(req.body.options || '{}')

      // Convert multer files to File objects
      const files = req.files.map(file => new File([file.buffer], file.originalname, {
        type: file.mimetype
      }))

      // TODO: Implement backend file handling that doesn't rely on renderer process
      const result = { success: false, errors: [{ message: 'Backend file handling not implemented' }] }
      
      res.json({
        success: result.success,
        data: result.success ? {
          files: [],
          uploadId: req.requestId
        } : undefined,
        error: result.success ? undefined : {
          code: 'UPLOAD_FAILED',
          message: 'File upload failed',
          details: result.errors
        }
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleFileRead(req: ApiRequest, res: Response): Promise<void> {
    try {
      // Implementation for file reading
      res.json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'File read endpoint not yet implemented'
        }
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleFileList(req: ApiRequest, res: Response): Promise<void> {
    try {
      // Implementation for file listing
      res.json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'File list endpoint not yet implemented'
        }
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleFileDelete(req: ApiRequest, res: Response): Promise<void> {
    try {
      // Implementation for file deletion
      res.json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'File delete endpoint not yet implemented'
        }
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleServiceStatus(req: ApiRequest, res: Response): Promise<void> {
    try {
      // const status = fileRoutingService.getStatus()
      const status = { initialized: true, healthy: true } // TODO: Implement proper status check
      res.json({
        success: true,
        data: status
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private async handleServiceMetrics(req: ApiRequest, res: Response): Promise<void> {
    try {
      // Implementation for service metrics
      res.json({
        success: false,
        error: {
          code: 'NOT_IMPLEMENTED',
          message: 'Service metrics endpoint not yet implemented'
        }
      })
    } catch (error) {
      this.handleError(error, res)
    }
  }

  private handleError(error: any, res: Response): void {
    this.logger.error('API error', 'handleError', error)

    if (error instanceof ServiceError) {
      res.status(400).json({
        success: false,
        error: {
          code: error.code,
          message: error.message,
          details: error.context
        }
      })
    } else {
      res.status(500).json({
        success: false,
        error: {
          code: 'INTERNAL_ERROR',
          message: 'Internal server error'
        }
      })
    }
  }

  private errorHandler(error: Error, req: Request, res: Response, next: NextFunction): void {
    this.handleError(error, res)
  }

  public getApp(): express.Application {
    return this.app
  }

  public async stop(): Promise<void> {
    await this.cleanup()
  }
}

export { FileRoutingApiServer }
