/**
 * ServiceRegistry
 * Manages service registration, API key generation, and service metadata
 * Implements the service registration flow from the API documentation
 */

import { BaseService, ServiceError, ServiceErrorCode } from '../../../src/services/base'

export interface ServiceRegistrationRequest {
  serviceName: string
  version: string
  capabilities: string[]
  description: string
  developer: {
    name: string
    email: string
    organization?: string
  }
}

export interface ServiceRegistrationResponse {
  serviceId: string
  apiKey: string
  status: 'active' | 'pending' | 'suspended'
  capabilities: string[]
  createdAt: string
}

export interface RegisteredService {
  serviceId: string
  serviceName: string
  version: string
  apiKey: string
  capabilities: string[]
  description: string
  developer: {
    name: string
    email: string
    organization?: string
  }
  status: 'active' | 'pending' | 'suspended'
  createdAt: string
  lastAccess?: string
  accessCount: number
}

class ServiceRegistry extends BaseService {
  private services: Map<string, RegisteredService> = new Map()
  private apiKeys: Map<string, string> = new Map() // apiKey -> serviceId mapping

  constructor() {
    super({
      name: 'ServiceRegistry',
      autoInitialize: false
    })
  }

  protected async doInitialize(): Promise<void> {
    // In production, load services from database
    await this.loadServices()
    
    this.logger.info('ServiceRegistry initialized successfully', 'doInitialize', {
      serviceCount: this.services.size
    })
  }

  protected async doCleanup(): Promise<void> {
    // In production, save services to database
    await this.saveServices()
    
    this.services.clear()
    this.apiKeys.clear()
    this.logger.info('ServiceRegistry cleaned up', 'doCleanup')
  }

  /**
   * Register a new service
   */
  async registerService(request: ServiceRegistrationRequest): Promise<ServiceRegistrationResponse> {
    try {
      // Validate request
      this.validateRegistrationRequest(request)

      // Generate unique IDs
      const serviceId = this.generateServiceId()
      const apiKey = this.generateApiKey()

      // Create service record
      const service: RegisteredService = {
        serviceId,
        serviceName: request.serviceName,
        version: request.version,
        apiKey,
        capabilities: request.capabilities,
        description: request.description,
        developer: request.developer,
        status: 'active', // Auto-approve for now
        createdAt: new Date().toISOString(),
        accessCount: 0
      }

      // Store service
      this.services.set(serviceId, service)
      this.apiKeys.set(apiKey, serviceId)

      // In production, save to database
      await this.saveServices()

      const response: ServiceRegistrationResponse = {
        serviceId,
        apiKey,
        status: service.status,
        capabilities: service.capabilities,
        createdAt: service.createdAt
      }

      this.logger.info('Service registered successfully', 'registerService', {
        serviceId,
        serviceName: request.serviceName,
        developer: request.developer.email
      })

      return response
    } catch (error) {
      this.logger.error('Failed to register service', 'registerService', error)
      throw error
    }
  }

  /**
   * Get service by ID
   */
  async getService(serviceId: string): Promise<RegisteredService | null> {
    return this.services.get(serviceId) || null
  }

  /**
   * Get service by API key
   */
  async getServiceByApiKey(apiKey: string): Promise<RegisteredService | null> {
    const serviceId = this.apiKeys.get(apiKey)
    if (!serviceId) {
      return null
    }
    return this.services.get(serviceId) || null
  }

  /**
   * Update service status
   */
  async updateServiceStatus(serviceId: string, status: RegisteredService['status']): Promise<void> {
    try {
      const service = this.services.get(serviceId)
      if (!service) {
        throw new ServiceError(
          ServiceErrorCode.NOT_FOUND,
          'Service not found',
          { serviceName: this.serviceName, operation: 'updateServiceStatus', serviceId }
        )
      }

      service.status = status
      this.services.set(serviceId, service)

      // In production, update database
      await this.saveServices()

      this.logger.info('Service status updated', 'updateServiceStatus', {
        serviceId,
        status
      })
    } catch (error) {
      this.logger.error('Failed to update service status', 'updateServiceStatus', error)
      throw error
    }
  }

  /**
   * Record service access
   */
  async recordAccess(serviceId: string): Promise<void> {
    try {
      const service = this.services.get(serviceId)
      if (service) {
        service.accessCount++
        service.lastAccess = new Date().toISOString()
        this.services.set(serviceId, service)
      }
    } catch (error) {
      this.logger.error('Failed to record access', 'recordAccess', error)
    }
  }

  /**
   * List all services
   */
  async listServices(): Promise<RegisteredService[]> {
    return Array.from(this.services.values())
  }

  /**
   * Revoke service (suspend and invalidate API key)
   */
  async revokeService(serviceId: string): Promise<void> {
    try {
      const service = this.services.get(serviceId)
      if (!service) {
        throw new ServiceError(
          ServiceErrorCode.NOT_FOUND,
          'Service not found',
          { serviceName: this.serviceName, operation: 'revokeService', serviceId }
        )
      }

      // Update status
      service.status = 'suspended'
      this.services.set(serviceId, service)

      // Remove API key mapping
      this.apiKeys.delete(service.apiKey)

      // In production, update database
      await this.saveServices()

      this.logger.info('Service revoked', 'revokeService', {
        serviceId,
        serviceName: service.serviceName
      })
    } catch (error) {
      this.logger.error('Failed to revoke service', 'revokeService', error)
      throw error
    }
  }

  /**
   * Validate registration request
   */
  private validateRegistrationRequest(request: ServiceRegistrationRequest): void {
    const errors: string[] = []

    if (!request.serviceName || request.serviceName.trim().length === 0) {
      errors.push('Service name is required')
    }

    if (!request.version || request.version.trim().length === 0) {
      errors.push('Version is required')
    }

    if (!request.capabilities || request.capabilities.length === 0) {
      errors.push('At least one capability is required')
    }

    if (!request.developer?.name || request.developer.name.trim().length === 0) {
      errors.push('Developer name is required')
    }

    if (!request.developer?.email || !this.isValidEmail(request.developer.email)) {
      errors.push('Valid developer email is required')
    }

    // Validate capabilities
    const validCapabilities = [
      'file.upload', 'file.read', 'file.list', 'file.delete',
      'context.read', 'context.write', 'vault.read'
    ]
    
    const invalidCapabilities = request.capabilities.filter(cap => !validCapabilities.includes(cap))
    if (invalidCapabilities.length > 0) {
      errors.push(`Invalid capabilities: ${invalidCapabilities.join(', ')}`)
    }

    if (errors.length > 0) {
      throw new ServiceError(
        ServiceErrorCode.VALIDATION_ERROR,
        'Registration validation failed',
        { serviceName: this.serviceName, operation: 'validateRegistrationRequest', errors }
      )
    }
  }

  /**
   * Generate unique service ID
   */
  private generateServiceId(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 11)
    return `srv_${timestamp}_${random}`
  }

  /**
   * Generate secure API key
   */
  private generateApiKey(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2, 15)
    const random2 = Math.random().toString(36).substring(2, 15)
    return `ak_${timestamp}_${random}${random2}`
  }

  /**
   * Validate email format
   */
  private isValidEmail(email: string): boolean {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  }

  /**
   * Load services from storage (mock implementation)
   */
  private async loadServices(): Promise<void> {
    try {
      // In production, load from SQLite database
      // For now, start with empty registry
      this.logger.debug('Services loaded from storage', 'loadServices')
    } catch (error) {
      this.logger.error('Failed to load services', 'loadServices', error)
    }
  }

  /**
   * Save services to storage (mock implementation)
   */
  private async saveServices(): Promise<void> {
    try {
      // In production, save to SQLite database
      this.logger.debug('Services saved to storage', 'saveServices', {
        serviceCount: this.services.size
      })
    } catch (error) {
      this.logger.error('Failed to save services', 'saveServices', error)
    }
  }

  /**
   * Get service statistics
   */
  async getServiceStats(): Promise<{
    totalServices: number
    activeServices: number
    suspendedServices: number
    totalApiCalls: number
  }> {
    const services = Array.from(this.services.values())
    
    return {
      totalServices: services.length,
      activeServices: services.filter(s => s.status === 'active').length,
      suspendedServices: services.filter(s => s.status === 'suspended').length,
      totalApiCalls: services.reduce((sum, s) => sum + s.accessCount, 0)
    }
  }
}

export { ServiceRegistry }
