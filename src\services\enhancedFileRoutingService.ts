/**
 * EnhancedFileRoutingService
 * Implements 2-step path translation for reliable cross-platform file handling
 * 
 * This service replaces the problematic content-transfer approach with a secure
 * path-based system that works reliably across Windows, macOS, and Linux.
 */

import { createServiceLogger } from './base/ServiceLogger'
import { crossPlatformPathResolver, PathTranslationRequest } from './crossPlatformPathResolver'
import { contextVaultService } from './contextVaultService'

export interface EnhancedFileUploadOptions {
  validateBeforeUpload?: boolean
  createMissingDirectories?: boolean
  overwriteExisting?: boolean
  generateMetadata?: boolean
  indexAfterUpload?: boolean
}

export interface FileUploadSession {
  sessionId: string
  fileCount: number
  startTime: number
  status: 'extracting' | 'translating' | 'uploading' | 'completed' | 'failed'
  progress: number
}

export interface FileUploadResponse {
  success: boolean
  files: UploadedFile[]
  errors: FileError[]
  sessionId: string
}

export interface UploadedFile {
  originalPath: string
  vaultPath: string
  fileName: string
  size: number
  mimeType: string
  checksum: string
}

export interface FileError {
  filePath: string
  error: string
  code: string
}

export interface FileDestination {
  contextId: string
  folderPath?: string
}

export interface ValidationResult {
  valid: boolean
  errors: FileValidationError[]
}

export interface FileValidationError {
  filename: string
  error: string
  message: string
}

export class EnhancedFileRoutingService {
  private activeSessions: Map<string, FileUploadSession> = new Map()
  private logger: any

  constructor() {
    this.logger = createServiceLogger('EnhancedFileRoutingService')
  }

  /**
   * Main entry point - 2-step file upload process
   */
  async handleFileDrop(
    files: File[],
    destination: FileDestination,
    options: EnhancedFileUploadOptions = {}
  ): Promise<FileUploadResponse> {
    try {
      return await this.performFileUpload(files, destination, options)
    } catch (error) {
      this.logger.error('File drop failed', error)
      throw error
    }
  }

  private async performFileUpload(
    files: File[],
    destination: FileDestination,
    options: EnhancedFileUploadOptions = {}
  ): Promise<FileUploadResponse> {
    const startTime = Date.now()

    // Validate files first if requested
    if (options.validateBeforeUpload !== false) {
      const validation = this.validateFiles(files)
      if (!validation.valid) {
        return {
          success: false,
          files: [],
          errors: validation.errors.map(err => ({
            filePath: err.filename,
            error: err.message,
            code: err.error
          })),
          sessionId: 'validation_failed'
        }
      }
    }

    // Step 1: Extract and store file paths
    const sessionId = await this.extractFilePaths(files)

    // Step 2: Translate paths to vault structure
    const translationResult = await this.translatePaths(sessionId, destination)

    if (!translationResult.success) {
      return {
        success: false,
        files: [],
        errors: translationResult.errors?.map(err => ({
          filePath: err.fileName,
          error: err.error,
          code: err.code
        })) || [],
        sessionId
      }
    }

    // Step 3: Execute file operations using translated paths
    const uploadResult = await this.executeFileOperations(translationResult.translations, options)

    // Record metrics
    this.logger.debug('File drop operation completed', {
      fileCount: files.length,
      duration: Date.now() - startTime,
      success: uploadResult.success
    })

    return uploadResult
  }

  /**
   * Step 1: Extract file paths securely
   */
  private async extractFilePaths(files: File[]): Promise<string> {
    this.logger.info(`Starting path extraction for ${files.length} files`, 'extractFilePaths')
    
    try {
      const sessionId = await crossPlatformPathResolver.extractAndStoreFilePaths(files)
      
      // Track session
      this.activeSessions.set(sessionId, {
        sessionId,
        fileCount: files.length,
        startTime: Date.now(),
        status: 'extracting',
        progress: 33
      })

      this.logger.info(`Path extraction completed`, 'extractFilePaths', { sessionId })
      return sessionId
    } catch (error: any) {
      this.logger.error('Path extraction failed', error)
      throw new Error(`Path extraction failed: ${error.message}`)
    }
  }

  /**
   * Step 2: Translate paths to vault structure
   */
  private async translatePaths(sessionId: string, destination: FileDestination) {
    this.logger.info(`Starting path translation for session ${sessionId}`)

    // Update session status
    const session = this.activeSessions.get(sessionId)
    if (session) {
      session.status = 'translating'
      session.progress = 66
    }

    try {
      const request: PathTranslationRequest = {
        sourceFiles: [], // Will be retrieved from stored session
        destination: {
          contextId: destination.contextId,
          vaultPath: destination.folderPath,
          autoRoute: true
        }
      }

      const result = await crossPlatformPathResolver.translateToVaultPaths(sessionId, request)

      this.logger.info(`Path translation completed for session ${sessionId}`)

      return result
    } catch (error: any) {
      this.logger.error('Path translation failed', error)
      throw new Error(`Path translation failed: ${error.message}`)
    }
  }

  /**
   * Step 3: Execute file operations using translated paths
   */
  private async executeFileOperations(
    translations: any[],
    options: EnhancedFileUploadOptions
  ): Promise<FileUploadResponse> {
    this.logger.info(`Starting file operations for ${translations.length} files`)
    
    const results: UploadedFile[] = []
    const errors: FileError[] = []

    for (const translation of translations) {
      try {
        // Create destination directory if needed
        if (options.createMissingDirectories !== false) {
          await this.ensureDestinationDirectory(translation.destinationPath)
        }

        // Check for existing file
        if (!options.overwriteExisting) {
          const exists = await window.electronAPI.vault.pathExists(translation.destinationPath)
          if (exists.exists) {
            // Generate unique name
            translation.destinationPath = await this.generateUniquePath(translation.destinationPath)
          }
        }

        // Execute secure file copy (no content through IPC)
        const copyResult = await window.electronAPI.vault.copyFile(
          translation.sourcePath, 
          translation.destinationPath
        )

        if (copyResult.success) {
          const uploadedFile: UploadedFile = {
            originalPath: translation.sourcePath,
            vaultPath: translation.destinationPath,
            fileName: translation.fileName,
            size: await this.getFileSize(translation.sourcePath),
            mimeType: await this.getMimeType(translation.sourcePath),
            checksum: await this.calculateChecksum(translation.sourcePath)
          }

          // Index file if requested
          if (options.indexAfterUpload !== false) {
            await this.indexUploadedFile(translation.destinationPath)
          }

          results.push(uploadedFile)

          this.logger.debug(`File operation completed: ${translation.sourcePath} -> ${translation.destinationPath}`)
        } else {
          errors.push({
            filePath: translation.fileName,
            error: copyResult.error || 'File copy failed',
            code: 'COPY_FAILED'
          })
        }
      } catch (error: any) {
        this.logger.error(`File operation failed for ${translation.fileName}`, error)
        errors.push({
          filePath: translation.fileName,
          error: error.message || 'File operation failed',
          code: 'OPERATION_FAILED'
        })
      }
    }

    return {
      success: errors.length === 0,
      files: results,
      errors,
      sessionId: 'completed'
    }
  }

  /**
   * Validate files before processing
   */
  private validateFiles(files: File[]): ValidationResult {
    const errors: FileValidationError[] = []
    const maxSize = 100 * 1024 * 1024 // 100MB
    const allowedTypes = ['image/*', 'text/*', 'application/pdf', 'application/json']

    for (const file of files) {
      // Size validation
      if (file.size > maxSize) {
        errors.push({
          filename: file.name,
          error: 'FILE_TOO_LARGE',
          message: `File size exceeds ${maxSize / 1024 / 1024}MB limit`
        })
      }

      // Type validation
      if (!this.isAllowedType(file.type, allowedTypes)) {
        errors.push({
          filename: file.name,
          error: 'UNSUPPORTED_TYPE',
          message: `File type ${file.type} is not supported`
        })
      }

      // Name validation
      if (!this.isValidFilename(file.name)) {
        errors.push({
          filename: file.name,
          error: 'INVALID_FILENAME',
          message: 'Filename contains invalid characters'
        })
      }
    }

    return {
      valid: errors.length === 0,
      errors
    }
  }

  private isAllowedType(mimeType: string, allowedTypes: string[]): boolean {
    return allowedTypes.some(allowedType => {
      if (allowedType.endsWith('/*')) {
        const prefix = allowedType.slice(0, -2)
        return mimeType.startsWith(prefix)
      }
      return mimeType === allowedType
    })
  }

  private isValidFilename(filename: string): boolean {
    const invalidChars = /[<>:"/\\|?*]/
    return !invalidChars.test(filename) && filename.length > 0 && filename.length <= 255
  }

  /**
   * Ensure destination directory exists
   */
  private async ensureDestinationDirectory(filePath: string): Promise<void> {
    const dirPath = filePath.substring(0, filePath.lastIndexOf('/') || filePath.lastIndexOf('\\'))
    if (dirPath) {
      await window.electronAPI.vault.createDirectory(dirPath)
    }
  }

  /**
   * Generate unique path if file already exists
   */
  private async generateUniquePath(originalPath: string): Promise<string> {
    let counter = 1
    let uniquePath = originalPath

    while (true) {
      const exists = await window.electronAPI.vault.pathExists(uniquePath)
      if (!exists.exists) {
        break
      }

      const ext = originalPath.substring(originalPath.lastIndexOf('.'))
      const nameWithoutExt = originalPath.substring(0, originalPath.lastIndexOf('.'))
      uniquePath = `${nameWithoutExt}_${counter}${ext}`
      counter++
    }

    return uniquePath
  }

  /**
   * Get file size from path
   */
  private async getFileSize(filePath: string): Promise<number> {
    try {
      // For now, return a default size since we can't get actual stats
      // This will be properly implemented once the new IPC methods are available
      return 0
    } catch (error) {
      return 0
    }
  }

  /**
   * Get MIME type from file path
   */
  private async getMimeType(filePath: string): Promise<string> {
    const ext = filePath.substring(filePath.lastIndexOf('.') + 1).toLowerCase()
    const mimeMap: Record<string, string> = {
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'md': 'text/markdown',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'json': 'application/json',
      'js': 'text/javascript',
      'ts': 'text/typescript',
      'html': 'text/html',
      'css': 'text/css'
    }
    return mimeMap[ext] || 'application/octet-stream'
  }

  /**
   * Generate file metadata
   */
  private async generateFileMetadata(filePath: string): Promise<any> {
    try {
      return {
        uploadedAt: new Date().toISOString(),
        fileSize: await this.getFileSize(filePath),
        lastModified: new Date().toISOString(),
        checksum: await this.calculateChecksum(filePath)
      }
    } catch (error) {
      return {
        uploadedAt: new Date().toISOString(),
        error: 'Failed to generate metadata'
      }
    }
  }

  /**
   * Calculate file checksum
   */
  private async calculateChecksum(filePath: string): Promise<string> {
    try {
      // For now, return a default checksum since we can't calculate actual hash
      // This will be properly implemented once the new IPC methods are available
      return `checksum_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    } catch (error) {
      return 'checksum_unavailable'
    }
  }

  /**
   * Index uploaded file
   */
  private async indexUploadedFile(filePath: string): Promise<void> {
    try {
      await window.electronAPI.files.indexFile(filePath, true)
      this.logger.debug(`File indexed successfully`, 'indexUploadedFile', { filePath })
    } catch (error: any) {
      this.logger.warn(`Failed to index file`, 'indexUploadedFile', { filePath, error: error.message })
    }
  }

  /**
   * Get active upload sessions
   */
  getActiveSessions(): FileUploadSession[] {
    return Array.from(this.activeSessions.values())
  }

  /**
   * Get session status
   */
  getSessionStatus(sessionId: string): FileUploadSession | null {
    return this.activeSessions.get(sessionId) || null
  }

  /**
   * Cancel active session
   */
  cancelSession(sessionId: string): boolean {
    const session = this.activeSessions.get(sessionId)
    if (session) {
      session.status = 'failed'
      this.activeSessions.delete(sessionId)
      return true
    }
    return false
  }

  /**
   * Clean up completed sessions
   */
  cleanupCompletedSessions(): void {
    const now = Date.now()
    const maxAge = 10 * 60 * 1000 // 10 minutes

    for (const [sessionId, session] of Array.from(this.activeSessions.entries())) {
      if (now - session.startTime > maxAge || session.status === 'completed' || session.status === 'failed') {
        this.activeSessions.delete(sessionId)
      }
    }
  }
}

// Export singleton instance
export const enhancedFileRoutingService = new EnhancedFileRoutingService()
