/**
 * CrossPlatformPathResolver Service
 * 2-step path translation system for reliable cross-platform file handling
 * 
 * Step 1: Extract and store file paths from drop events
 * Step 2: Translate to context vault paths with OS-specific handling
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'
import { contextVaultService } from './contextVaultService'

export interface FilePathInfo {
  originalPath: string
  fileName: string
  fileSize: number
  mimeType: string
  lastModified: number
  isDirectory: boolean
}

export interface PathTranslationRequest {
  sourceFiles: FilePathInfo[]
  destination: {
    contextId?: string
    vaultPath?: string
    subPath?: string
    autoRoute?: boolean
  }
}

export interface PathTranslationResult {
  success: boolean
  translations: PathTranslation[]
  errors?: PathTranslationError[]
}

export interface PathTranslation {
  sourcePath: string
  destinationPath: string
  fileName: string
  relativePath: string
  autoRouteFolder?: string
}

export interface PathTranslationError {
  sourcePath: string
  fileName: string
  error: string
  code: string
}

export interface PlatformPathConfig {
  separator: string
  isWindows: boolean
  isMacOS: boolean
  isLinux: boolean
  homeDirectory: string
  documentsDirectory: string
}

export class CrossPlatformPathResolver extends BaseService {
  private platformConfig: PlatformPathConfig | null = null
  private pathStore: Map<string, FilePathInfo[]> = new Map()

  constructor() {
    super({
      name: 'CrossPlatformPathResolver',
      autoInitialize: true
    })
  }

  protected async doInitialize(): Promise<void> {
    // Initialize platform configuration
    await this.initializePlatformConfig()
    
    this.logger.info('CrossPlatformPathResolver initialized successfully', 'doInitialize', {
      platform: this.platformConfig?.isWindows ? 'Windows' : 
                this.platformConfig?.isMacOS ? 'macOS' : 'Linux',
      separator: this.platformConfig?.separator
    })
  }

  /**
   * Step 1: Extract file paths from drop event and store temporarily
   */
  async extractAndStoreFilePaths(files: File[]): Promise<string> {
    return await this.executeOperationOrThrow(
      'extractAndStoreFilePaths',
      async () => {
        const sessionId = this.generateSessionId()
        const fileInfos: FilePathInfo[] = []

        for (const file of files) {
          try {
            // Extract file path using secure method
            const filePath = await this.extractSecureFilePath(file)
            
            const fileInfo: FilePathInfo = {
              originalPath: filePath,
              fileName: file.name,
              fileSize: file.size,
              mimeType: file.type || this.inferMimeType(file.name),
              lastModified: file.lastModified,
              isDirectory: false // Files from drop events are always files
            }

            // Validate the extracted path
            await this.validateSourcePath(fileInfo)
            fileInfos.push(fileInfo)

          } catch (error: any) {
            this.logger.warn(`Failed to extract path for file: ${file.name}`, 'extractAndStoreFilePaths', error)
            // Continue with other files
          }
        }

        if (fileInfos.length === 0) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            'No valid file paths could be extracted',
            { serviceName: this.serviceName, operation: 'extractAndStoreFilePaths' }
          )
        }

        // Store in temporary session
        this.pathStore.set(sessionId, fileInfos)
        
        // Auto-cleanup after 5 minutes
        setTimeout(() => {
          this.pathStore.delete(sessionId)
        }, 5 * 60 * 1000)

        this.logger.info(`Extracted and stored ${fileInfos.length} file paths`, 'extractAndStoreFilePaths', {
          sessionId,
          fileCount: fileInfos.length
        })

        return sessionId
      },
      { fileCount: files.length }
    )
  }

  /**
   * Step 2: Translate stored paths to context vault paths
   */
  async translateToVaultPaths(sessionId: string, request: PathTranslationRequest): Promise<PathTranslationResult> {
    return await this.executeOperationOrThrow(
      'translateToVaultPaths',
      async () => {
        // Retrieve stored file paths
        const storedFiles = this.pathStore.get(sessionId)
        if (!storedFiles) {
          throw new ServiceError(
            ServiceErrorCode.VALIDATION_ERROR,
            `Session ${sessionId} not found or expired`,
            { serviceName: this.serviceName, operation: 'translateToVaultPaths' }
          )
        }

        const translations: PathTranslation[] = []
        const errors: PathTranslationError[] = []

        // Get vault configuration
        const vaultConfig = await this.resolveVaultConfiguration(request.destination)

        for (const fileInfo of storedFiles) {
          try {
            const translation = await this.translateSinglePath(fileInfo, vaultConfig, request.destination)
            translations.push(translation)
          } catch (error: any) {
            errors.push({
              sourcePath: fileInfo.originalPath,
              fileName: fileInfo.fileName,
              error: error.message,
              code: error.code || 'TRANSLATION_ERROR'
            })
          }
        }

        // Clean up session
        this.pathStore.delete(sessionId)

        return {
          success: errors.length === 0,
          translations,
          errors: errors.length > 0 ? errors : undefined
        }
      },
      { sessionId }
    )
  }

  /**
   * Initialize platform-specific configuration
   */
  private async initializePlatformConfig(): Promise<void> {
    try {
      const platform = await window.electronAPI.getOsPlatform()
      
      const isWindows = platform === 'win32'
      const isMacOS = platform === 'darwin'
      const isLinux = platform === 'linux'

      // Get platform-specific paths
      const homeDir = await this.getPlatformHomeDirectory()
      const docsDir = await this.getPlatformDocumentsDirectory()

      this.platformConfig = {
        separator: isWindows ? '\\' : '/',
        isWindows,
        isMacOS,
        isLinux,
        homeDirectory: homeDir,
        documentsDirectory: docsDir
      }

    } catch (error: any) {
      throw new ServiceError(
        ServiceErrorCode.INITIALIZATION_FAILED,
        `Failed to initialize platform configuration: ${error.message}`,
        { serviceName: this.serviceName, operation: 'initializePlatformConfig' }
      )
    }
  }

  /**
   * Extract secure file path from File object
   * Since Electron no longer provides File.path for security reasons,
   * we need to handle this differently for drag-and-drop vs file input
   */
  private async extractSecureFilePath(file: File): Promise<string> {
    // For directory uploads (folder drag-and-drop)
    if ('webkitRelativePath' in file && file.webkitRelativePath) {
      return file.webkitRelativePath
    }

    // For drag-and-drop files, we can't get the real path due to security restrictions
    // Instead, we'll create a temporary identifier and let the main process handle the file
    // The main process will receive the actual file data and create a temporary file

    // Generate a temporary file identifier based on file properties
    const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}_${file.name}`

    // Store the file data temporarily for the main process to access
    // We'll use a different approach - send the file to main process via IPC
    const arrayBuffer = await file.arrayBuffer()
    const uint8Array = new Uint8Array(arrayBuffer)

    // Send file data to main process and get back a temporary path
    const result = await window.electronAPI.files.createTempFileFromBuffer({
      fileName: file.name,
      buffer: uint8Array,
      mimeType: file.type,
      lastModified: file.lastModified
    })

    if (!result.success || !result.tempPath) {
      throw new Error(`Failed to create temporary file for: ${file.name}`)
    }

    return result.tempPath
  }

  /**
   * Validate source path for security
   */
  private async validateSourcePath(fileInfo: FilePathInfo): Promise<void> {
    const { originalPath } = fileInfo

    // Check if path exists
    const pathCheck = await window.electronAPI.vault.pathExists(originalPath)
    if (!pathCheck.exists) {
      throw new Error(`Source path does not exist: ${originalPath}`)
    }

    // Security validations
    await this.performSecurityValidations(originalPath)
  }

  /**
   * Perform security validations on source path
   */
  private async performSecurityValidations(filePath: string): Promise<void> {
    if (!this.platformConfig) {
      throw new Error('Platform configuration not initialized')
    }

    // Normalize path for consistent checking
    const normalizedPath = this.normalizePath(filePath)

    // Check for path traversal attempts
    if (normalizedPath.includes('..')) {
      throw new Error('Path traversal detected in file path')
    }

    // Platform-specific security checks
    if (this.platformConfig.isWindows) {
      await this.validateWindowsPath(normalizedPath)
    } else {
      await this.validateUnixPath(normalizedPath)
    }

    // Check file size limits
    // Additional validations can be added here
  }

  /**
   * Generate unique session ID
   */
  private generateSessionId(): string {
    return `path_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Infer MIME type from file extension
   */
  private inferMimeType(fileName: string): string {
    const ext = this.getFileExtension(fileName).toLowerCase()
    const mimeMap: Record<string, string> = {
      'pdf': 'application/pdf',
      'txt': 'text/plain',
      'md': 'text/markdown',
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'gif': 'image/gif',
      'webp': 'image/webp',
      'json': 'application/json',
      'js': 'text/javascript',
      'ts': 'text/typescript',
      'html': 'text/html',
      'css': 'text/css'
    }
    return mimeMap[ext] || 'application/octet-stream'
  }

  /**
   * Get file extension without dot
   */
  private getFileExtension(fileName: string): string {
    const lastDot = fileName.lastIndexOf('.')
    return lastDot > 0 ? fileName.substring(lastDot + 1) : ''
  }

  /**
   * Normalize path for cross-platform consistency
   */
  private normalizePath(filePath: string): string {
    if (!this.platformConfig) return filePath

    // Convert all separators to platform-specific
    return filePath.replace(/[\/\\]/g, this.platformConfig.separator)
  }

  /**
   * Get platform-specific home directory
   */
  private async getPlatformHomeDirectory(): Promise<string> {
    // This would typically come from the backend
    // For now, we'll use a placeholder that gets resolved by the backend
    return 'HOME_DIRECTORY_PLACEHOLDER'
  }

  /**
   * Get platform-specific documents directory
   */
  private async getPlatformDocumentsDirectory(): Promise<string> {
    // This would typically come from the backend
    return 'DOCUMENTS_DIRECTORY_PLACEHOLDER'
  }

  /**
   * Validate Windows-specific path constraints
   */
  private async validateWindowsPath(normalizedPath: string): Promise<void> {
    // Windows-specific validations
    const invalidChars = /[<>:"|?*]/
    if (invalidChars.test(normalizedPath)) {
      throw new Error('Path contains invalid Windows characters')
    }

    // Check for reserved names
    const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9']
    const fileName = normalizedPath.split('\\').pop()?.toUpperCase()
    if (fileName && reservedNames.includes(fileName)) {
      throw new Error('Path contains reserved Windows filename')
    }
  }

  /**
   * Validate Unix-specific path constraints
   */
  private async validateUnixPath(normalizedPath: string): Promise<void> {
    // Unix-specific validations
    if (normalizedPath.includes('\0')) {
      throw new Error('Path contains null character')
    }

    // Check for extremely long paths
    if (normalizedPath.length > 4096) {
      throw new Error('Path exceeds maximum length')
    }
  }

  /**
   * Resolve vault configuration for destination
   */
  private async resolveVaultConfiguration(destination: any): Promise<any> {
    if (destination.contextId) {
      // Get context vault configuration
      await contextVaultService.ensureInitialized()
      const contextResult = contextVaultService.findContextById(destination.contextId)

      if (!contextResult) {
        throw new ServiceError(
          ServiceErrorCode.VALIDATION_ERROR,
          `Context ${destination.contextId} not found`,
          { serviceName: this.serviceName, operation: 'resolveVaultConfiguration' }
        )
      }

      return {
        type: 'context',
        basePath: contextResult.context.path,
        contextId: destination.contextId,
        vaultName: contextResult.vault.name
      }
    } else if (destination.vaultPath) {
      // Explicit vault path
      return {
        type: 'explicit',
        basePath: destination.vaultPath
      }
    } else {
      // Shared dropbox
      const vaultRoot = await this.getVaultRoot()
      return {
        type: 'shared',
        basePath: this.joinPlatformPath(vaultRoot, 'shared-dropbox')
      }
    }
  }

  /**
   * Translate single file path to vault path
   */
  private async translateSinglePath(
    fileInfo: FilePathInfo,
    vaultConfig: any,
    destination: any
  ): Promise<PathTranslation> {
    let destinationPath: string
    let relativePath: string
    let autoRouteFolder: string | undefined

    if (destination.subPath) {
      // Explicit sub-path specified
      relativePath = this.joinPlatformPath(destination.subPath, fileInfo.fileName)
      destinationPath = this.joinPlatformPath(vaultConfig.basePath, relativePath)
    } else if (destination.autoRoute !== false) {
      // Auto-route based on file type
      autoRouteFolder = this.getAutoRouteFolder(fileInfo.mimeType)
      relativePath = this.joinPlatformPath(autoRouteFolder, fileInfo.fileName)
      destinationPath = this.joinPlatformPath(vaultConfig.basePath, relativePath)
    } else {
      // Direct to base path
      relativePath = fileInfo.fileName
      destinationPath = this.joinPlatformPath(vaultConfig.basePath, fileInfo.fileName)
    }

    // Ensure unique filename if file already exists
    destinationPath = await this.ensureUniqueDestinationPath(destinationPath)

    return {
      sourcePath: fileInfo.originalPath,
      destinationPath,
      fileName: fileInfo.fileName,
      relativePath,
      autoRouteFolder
    }
  }

  /**
   * Get auto-route folder based on MIME type
   */
  private getAutoRouteFolder(mimeType: string): string {
    if (mimeType.startsWith('image/')) return 'images'
    if (mimeType.startsWith('text/') || mimeType === 'application/pdf') return 'documents'
    if (mimeType.includes('javascript') || mimeType.includes('json')) return 'code'
    if (mimeType.includes('zip') || mimeType.includes('archive')) return 'archives'
    return 'documents'
  }

  /**
   * Join paths using platform-specific separator
   */
  private joinPlatformPath(...parts: string[]): string {
    if (!this.platformConfig) {
      return parts.join('/')
    }

    return parts
      .filter(part => part && part.length > 0)
      .join(this.platformConfig.separator)
      .replace(/[\/\\]+/g, this.platformConfig.separator)
  }

  /**
   * Ensure destination path is unique
   */
  private async ensureUniqueDestinationPath(basePath: string): Promise<string> {
    let finalPath = basePath
    let counter = 1

    while (true) {
      const pathCheck = await window.electronAPI.vault.pathExists(finalPath)
      if (!pathCheck.exists) {
        break
      }

      // Generate unique name
      const ext = this.getFileExtension(basePath)
      const nameWithoutExt = basePath.substring(0, basePath.lastIndexOf('.'))
      finalPath = ext ? `${nameWithoutExt}_${counter}.${ext}` : `${basePath}_${counter}`
      counter++
    }

    return finalPath
  }

  /**
   * Get vault root path
   */
  private async getVaultRoot(): Promise<string> {
    const registry = await window.electronAPI.vault.getVaultRegistry()
    if (!registry?.vaultRoot) {
      throw new ServiceError(
        ServiceErrorCode.CONFIGURATION_ERROR,
        'No vault root configured',
        { serviceName: this.serviceName, operation: 'getVaultRoot' }
      )
    }
    return registry.vaultRoot
  }

  /**
   * Get stored file paths for a session
   */
  getStoredFilePaths(sessionId: string): FilePathInfo[] | null {
    return this.pathStore.get(sessionId) || null
  }

  /**
   * Clear expired sessions manually
   */
  clearExpiredSessions(): void {
    // In a production system, you might want to track session timestamps
    // and clear based on age rather than relying only on setTimeout
    this.logger.info(`Cleared expired path sessions`, 'clearExpiredSessions')
  }
}

// Export singleton instance
export const crossPlatformPathResolver = new CrossPlatformPathResolver()
