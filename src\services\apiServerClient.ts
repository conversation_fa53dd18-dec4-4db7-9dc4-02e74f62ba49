/**
 * API Server Client
 * Client service for communicating with the FileRoutingService API Server
 * Provides admin dashboard functionality for API key management and monitoring
 */

import { BaseService, ServiceError, ServiceErrorCode } from './base'

export interface ServiceRegistrationRequest {
  name: string
  version: string
  description?: string
  capabilities: string[]
  developerInfo: {
    name: string
    email: string
    organization?: string
  }
}

export interface ServiceRegistrationResponse {
  serviceId: string
  apiKey: string
  status: 'active'
  createdAt: string
}

export interface RegisteredService {
  serviceId: string
  name: string
  version: string
  description?: string
  apiKeyHash: string
  capabilities: string[]
  developerInfo: {
    name: string
    email: string
    organization?: string
  }
  status: 'active' | 'suspended' | 'revoked'
  createdAt: string
  lastAccess?: string
  totalRequests: number
  successfulRequests: number
  failedRequests: number
}

export interface AccessLogEntry {
  id: string
  serviceId: string
  endpoint: string
  method: string
  success: boolean
  ip: string
  userAgent?: string
  timestamp: string
  responseTime?: number
  errorCode?: string
  errorMessage?: string
}

export interface SecurityEvent {
  id: string
  type: 'FAILED_AUTH' | 'RATE_LIMIT' | 'SUSPICIOUS_ACTIVITY' | 'TOKEN_EXPIRED'
  serviceId?: string
  ip: string
  userAgent?: string
  details: any
  timestamp: string
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL'
}

export interface AccessStats {
  totalRequests: number
  successfulRequests: number
  failedRequests: number
  averageResponseTime: number
  topEndpoints: Array<{ endpoint: string; count: number }>
  recentErrors: Array<{ errorCode: string; count: number }>
}

class ApiServerClient extends BaseService {
  private baseUrl: string
  private adminApiKey: string | null = null

  constructor() {
    super('ApiServerClient')
    this.baseUrl = 'http://localhost:3001' // API server URL
  }

  protected async doInitialize(): Promise<void> {
    // In a real implementation, this would get the admin API key from secure storage
    // For now, we'll use a mock admin key
    this.adminApiKey = 'admin_key_12345'
    this.logger.info('API Server Client initialized', 'doInitialize')
  }

  protected async doCleanup(): Promise<void> {
    this.adminApiKey = null
    this.logger.info('API Server Client cleaned up', 'doCleanup')
  }

  /**
   * Make an authenticated request to the API server
   */
  private async makeRequest<T>(
    endpoint: string,
    method: 'GET' | 'POST' | 'PUT' | 'DELETE' = 'GET',
    body?: any
  ): Promise<T> {
    try {
      const headers: Record<string, string> = {
        'Content-Type': 'application/json'
      }

      if (this.adminApiKey) {
        headers['X-API-Key'] = this.adminApiKey
      }

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        method,
        headers,
        body: body ? JSON.stringify(body) : undefined
      })

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.error || `HTTP ${response.status}`)
      }

      return await response.json()
    } catch (error) {
      this.logger.error('API request failed', 'makeRequest', { endpoint, method, error })
      throw new ServiceError(
        ServiceErrorCode.NETWORK_ERROR,
        `Failed to communicate with API server: ${error instanceof Error ? error.message : 'Unknown error'}`,
        { serviceName: this.serviceName, operation: 'makeRequest', endpoint, method }
      )
    }
  }

  /**
   * Register a new service and get API key
   */
  async registerService(request: ServiceRegistrationRequest): Promise<ServiceRegistrationResponse> {
    return await this.executeOperationOrThrow(
      'registerService',
      async () => {
        return await this.makeRequest<ServiceRegistrationResponse>('/api/v1/admin/services/register', 'POST', request)
      },
      { request }
    )
  }

  /**
   * Get all registered services
   */
  async getRegisteredServices(): Promise<RegisteredService[]> {
    return await this.executeOperationOrThrow(
      'getRegisteredServices',
      async () => {
        return await this.makeRequest<RegisteredService[]>('/api/v1/admin/services')
      }
    )
  }

  /**
   * Get service by ID
   */
  async getServiceById(serviceId: string): Promise<RegisteredService | null> {
    return await this.executeOperationOrThrow(
      'getServiceById',
      async () => {
        try {
          return await this.makeRequest<RegisteredService>(`/api/v1/admin/services/${serviceId}`)
        } catch (error) {
          if (error instanceof ServiceError && error.message.includes('404')) {
            return null
          }
          throw error
        }
      },
      { serviceId }
    )
  }

  /**
   * Update service status
   */
  async updateServiceStatus(serviceId: string, status: 'active' | 'suspended' | 'revoked', reason?: string): Promise<void> {
    return await this.executeOperationOrThrow(
      'updateServiceStatus',
      async () => {
        await this.makeRequest(`/api/v1/admin/services/${serviceId}/status`, 'PUT', { status, reason })
      },
      { serviceId, status, reason }
    )
  }

  /**
   * Get access statistics for a service
   */
  async getAccessStats(serviceId: string, timeRange?: { start: Date; end: Date }): Promise<AccessStats> {
    return await this.executeOperationOrThrow(
      'getAccessStats',
      async () => {
        const params = new URLSearchParams()
        if (timeRange) {
          params.append('start', timeRange.start.toISOString())
          params.append('end', timeRange.end.toISOString())
        }
        const query = params.toString() ? `?${params.toString()}` : ''
        return await this.makeRequest<AccessStats>(`/api/v1/admin/services/${serviceId}/stats${query}`)
      },
      { serviceId, timeRange }
    )
  }

  /**
   * Get recent access logs
   */
  async getRecentLogs(limit: number = 100, serviceId?: string): Promise<AccessLogEntry[]> {
    return await this.executeOperationOrThrow(
      'getRecentLogs',
      async () => {
        const params = new URLSearchParams()
        params.append('limit', limit.toString())
        if (serviceId) {
          params.append('serviceId', serviceId)
        }
        return await this.makeRequest<AccessLogEntry[]>(`/api/v1/admin/logs?${params.toString()}`)
      },
      { limit, serviceId }
    )
  }

  /**
   * Get recent security events
   */
  async getSecurityEvents(limit: number = 50, severity?: string): Promise<SecurityEvent[]> {
    return await this.executeOperationOrThrow(
      'getSecurityEvents',
      async () => {
        const params = new URLSearchParams()
        params.append('limit', limit.toString())
        if (severity) {
          params.append('severity', severity)
        }
        return await this.makeRequest<SecurityEvent[]>(`/api/v1/admin/security-events?${params.toString()}`)
      },
      { limit, severity }
    )
  }

  /**
   * Get admin dashboard statistics
   */
  async getDashboardStats(): Promise<any> {
    return await this.executeOperationOrThrow(
      'getDashboardStats',
      async () => {
        return await this.makeRequest('/api/v1/admin/stats')
      }
    )
  }

  /**
   * Export logs for compliance/audit purposes
   */
  async exportLogs(format: 'json' | 'csv', timeRange?: { start: Date; end: Date }): Promise<string> {
    return await this.executeOperationOrThrow(
      'exportLogs',
      async () => {
        const params = new URLSearchParams()
        params.append('format', format)
        if (timeRange) {
          params.append('start', timeRange.start.toISOString())
          params.append('end', timeRange.end.toISOString())
        }
        return await this.makeRequest<string>(`/api/v1/admin/export?${params.toString()}`)
      },
      { format, timeRange }
    )
  }

  /**
   * Generate a secure API key
   */
  generateAPIKey(): string {
    const prefix = 'chatlo_'
    const randomBytes = new Uint8Array(32)
    crypto.getRandomValues(randomBytes)
    const key = Array.from(randomBytes, byte => byte.toString(16).padStart(2, '0')).join('')
    return prefix + key
  }

  /**
   * Hash an API key for storage
   */
  async hashAPIKey(apiKey: string): Promise<string> {
    const encoder = new TextEncoder()
    const data = encoder.encode(apiKey)
    const hashBuffer = await crypto.subtle.digest('SHA-256', data)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }
}

// Singleton instance
export const apiServerClient = new ApiServerClient()

// Legacy compatibility - create a serviceAccessLogger alias
export const serviceAccessLogger = {
  async registerService(request: any): Promise<string> {
    const response = await apiServerClient.registerService(request)
    return response.serviceId
  },

  async getRegisteredServices(): Promise<any[]> {
    return await apiServerClient.getRegisteredServices()
  },

  async updateServiceStatus(serviceId: string, status: string, reason?: string): Promise<void> {
    await apiServerClient.updateServiceStatus(serviceId, status as any, reason)
  },

  async getAccessStats(serviceId: string, timeRange?: any): Promise<any> {
    return await apiServerClient.getAccessStats(serviceId, timeRange)
  },

  async getRecentLogs(limit?: number, serviceId?: string): Promise<any[]> {
    return await apiServerClient.getRecentLogs(limit, serviceId)
  },

  async getSecurityEvents(limit?: number): Promise<any[]> {
    return await apiServerClient.getSecurityEvents(limit)
  },

  async getServiceAccessReport(): Promise<any[]> {
    return await apiServerClient.getRegisteredServices()
  },

  async exportLogs(format: string, timeRange?: any): Promise<string> {
    return await apiServerClient.exportLogs(format as any, timeRange)
  }
}
