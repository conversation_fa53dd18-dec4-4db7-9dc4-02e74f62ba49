/**
 * Enhanced2StepDropZone Component
 * Implements 2-step path translation for reliable cross-platform file uploads
 * 
 * Features:
 * - Secure path extraction using native dialogs
 * - Cross-platform path translation
 * - Progress tracking for multi-step process
 * - Error handling with detailed feedback
 */

import React, { useState, useCallback, useRef } from 'react'
import { enhancedFileRoutingService } from '../services/enhancedFileRoutingService'
import { FileDestination, FileUploadResponse } from '../types/fileRouting'

interface Enhanced2StepDropZoneProps {
  destination: FileDestination
  onUploadComplete?: (response: FileUploadResponse) => void
  onUploadError?: (error: string) => void
  onUploadProgress?: (progress: number) => void
  className?: string
  children?: React.ReactNode
  disabled?: boolean
  acceptedTypes?: string[]
  maxFiles?: number
  maxFileSize?: number
}

interface UploadState {
  isDragging: boolean
  isUploading: boolean
  progress: number
  stage: 'idle' | 'extracting' | 'translating' | 'uploading' | 'completed' | 'error'
  sessionId?: string
  error?: string
}

export const Enhanced2StepDropZone: React.FC<Enhanced2StepDropZoneProps> = ({
  destination,
  onUploadComplete,
  onUploadError,
  onUploadProgress,
  className = '',
  children,
  disabled = false,
  acceptedTypes = ['*/*'],
  maxFiles = 10,
  maxFileSize = 100 * 1024 * 1024 // 100MB
}) => {
  const [uploadState, setUploadState] = useState<UploadState>({
    isDragging: false,
    isUploading: false,
    progress: 0,
    stage: 'idle'
  })

  const fileInputRef = useRef<HTMLInputElement>(null)
  const dragCounterRef = useRef(0)

  const updateProgress = useCallback((progress: number, stage: UploadState['stage']) => {
    setUploadState(prev => ({ ...prev, progress, stage }))
    onUploadProgress?.(progress)
  }, [onUploadProgress])

  const handleFiles = useCallback(async (files: File[]) => {
    if (disabled || files.length === 0) return

    // Validate file count
    if (files.length > maxFiles) {
      const error = `Too many files. Maximum ${maxFiles} files allowed.`
      setUploadState(prev => ({ ...prev, error, stage: 'error' }))
      onUploadError?.(error)
      return
    }

    // Validate file sizes
    const oversizedFiles = files.filter(file => file.size > maxFileSize)
    if (oversizedFiles.length > 0) {
      const error = `Files too large: ${oversizedFiles.map(f => f.name).join(', ')}`
      setUploadState(prev => ({ ...prev, error, stage: 'error' }))
      onUploadError?.(error)
      return
    }

    try {
      setUploadState(prev => ({ 
        ...prev, 
        isUploading: true, 
        stage: 'extracting', 
        progress: 0, 
        error: undefined 
      }))

      // Initialize the enhanced file routing service
      if (!enhancedFileRoutingService.getStatus().initialized) {
        await enhancedFileRoutingService.initialize()
      }

      updateProgress(10, 'extracting')

      // Execute 2-step upload process
      const response = await enhancedFileRoutingService.handleFileDrop(files, destination, {
        validateBeforeUpload: true,
        createMissingDirectories: true,
        overwriteExisting: false,
        generateMetadata: true,
        indexAfterUpload: true
      })

      updateProgress(100, 'completed')

      if (response.success) {
        setUploadState(prev => ({ 
          ...prev, 
          isUploading: false, 
          stage: 'completed',
          progress: 100 
        }))
        onUploadComplete?.(response)
      } else {
        const errorMessage = response.errors?.map(e => e.message).join(', ') || 'Upload failed'
        setUploadState(prev => ({ 
          ...prev, 
          isUploading: false, 
          stage: 'error',
          error: errorMessage 
        }))
        onUploadError?.(errorMessage)
      }

    } catch (error: any) {
      const errorMessage = error.message || 'Upload failed unexpectedly'
      setUploadState(prev => ({ 
        ...prev, 
        isUploading: false, 
        stage: 'error',
        error: errorMessage 
      }))
      onUploadError?.(errorMessage)
    }
  }, [disabled, maxFiles, maxFileSize, destination, onUploadComplete, onUploadError, updateProgress])

  const handleDragEnter = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    dragCounterRef.current++
    if (dragCounterRef.current === 1) {
      setUploadState(prev => ({ ...prev, isDragging: true }))
    }
  }, [])

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    dragCounterRef.current--
    if (dragCounterRef.current === 0) {
      setUploadState(prev => ({ ...prev, isDragging: false }))
    }
  }, [])

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    
    dragCounterRef.current = 0
    setUploadState(prev => ({ ...prev, isDragging: false }))

    if (disabled || uploadState.isUploading) return

    const files = Array.from(e.dataTransfer.files)
    handleFiles(files)
  }, [disabled, uploadState.isUploading, handleFiles])

  const handleFileInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || [])
    if (files.length > 0) {
      handleFiles(files)
    }
    // Reset input value to allow selecting the same file again
    e.target.value = ''
  }, [handleFiles])

  const handleClick = useCallback(() => {
    if (!disabled && !uploadState.isUploading) {
      fileInputRef.current?.click()
    }
  }, [disabled, uploadState.isUploading])

  const getStageText = () => {
    switch (uploadState.stage) {
      case 'extracting': return 'Extracting file paths...'
      case 'translating': return 'Translating to vault paths...'
      case 'uploading': return 'Copying files...'
      case 'completed': return 'Upload completed!'
      case 'error': return `Error: ${uploadState.error}`
      default: return 'Drop files here or click to select'
    }
  }

  const getProgressColor = () => {
    switch (uploadState.stage) {
      case 'error': return 'bg-red-500'
      case 'completed': return 'bg-green-500'
      default: return 'bg-blue-500'
    }
  }

  return (
    <div
      className={`
        relative border-2 border-dashed rounded-lg p-6 text-center cursor-pointer
        transition-all duration-200 ease-in-out
        ${uploadState.isDragging 
          ? 'border-blue-400 bg-blue-50 dark:bg-blue-900/20' 
          : 'border-gray-300 dark:border-gray-600 hover:border-gray-400 dark:hover:border-gray-500'
        }
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${uploadState.stage === 'error' ? 'border-red-400 bg-red-50 dark:bg-red-900/20' : ''}
        ${uploadState.stage === 'completed' ? 'border-green-400 bg-green-50 dark:bg-green-900/20' : ''}
        ${className}
      `}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDragOver={handleDragOver}
      onDrop={handleDrop}
      onClick={handleClick}
    >
      <input
        ref={fileInputRef}
        type="file"
        multiple
        accept={acceptedTypes.join(',')}
        onChange={handleFileInputChange}
        className="hidden"
        disabled={disabled}
      />

      {children || (
        <div className="space-y-4">
          <div className="text-lg font-medium text-gray-700 dark:text-gray-300">
            {getStageText()}
          </div>
          
          {uploadState.isUploading && (
            <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
              <div 
                className={`h-2 rounded-full transition-all duration-300 ${getProgressColor()}`}
                style={{ width: `${uploadState.progress}%` }}
              />
            </div>
          )}

          <div className="text-sm text-gray-500 dark:text-gray-400">
            <p>Supports: {acceptedTypes.includes('*/*') ? 'All file types' : acceptedTypes.join(', ')}</p>
            <p>Max {maxFiles} files, {Math.round(maxFileSize / 1024 / 1024)}MB each</p>
            <p className="mt-2 text-xs">
              🔒 Secure 2-step process: Path extraction → Vault translation → File copy
            </p>
          </div>
        </div>
      )}
    </div>
  )
}

export default Enhanced2StepDropZone
