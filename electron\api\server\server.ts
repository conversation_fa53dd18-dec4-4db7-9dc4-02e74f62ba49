/**
 * FileRoutingService API Server Startup
 * Main entry point for the HTTP API server
 */

import { FileRoutingApiServer, ApiServerConfig } from './fileRoutingApiServer'

// Default configuration
const defaultConfig: ApiServerConfig = {
  port: 3001,
  host: 'localhost',
  corsOrigins: ['http://localhost:3000', 'http://localhost:5173'], // Common dev ports
  rateLimit: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 100 // limit each IP to 100 requests per windowMs
  },
  upload: {
    maxFileSize: 50 * 1024 * 1024, // 50MB
    maxFiles: 10,
    allowedMimeTypes: [
      'text/*',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-powerpoint',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'image/*',
      'video/*',
      'audio/*',
      'application/zip',
      'application/x-zip-compressed',
      'application/json',
      'application/xml'
    ]
  }
}

/**
 * Start the API server
 */
async function startServer(config: Partial<ApiServerConfig> = {}): Promise<FileRoutingApiServer> {
  const finalConfig = { ...defaultConfig, ...config }
  
  console.log('🚀 Starting FileRoutingService API Server...')
  console.log(`📍 Server will run on: http://${finalConfig.host}:${finalConfig.port}`)
  console.log(`🔒 CORS origins: ${finalConfig.corsOrigins.join(', ')}`)
  console.log(`📁 Max file size: ${finalConfig.upload.maxFileSize / (1024 * 1024)}MB`)
  console.log(`📊 Rate limit: ${finalConfig.rateLimit.max} requests per ${finalConfig.rateLimit.windowMs / 60000} minutes`)

  const server = new FileRoutingApiServer(finalConfig)
  
  try {
    await server.initialize()
    
    console.log('✅ FileRoutingService API Server started successfully!')
    console.log('')
    console.log('📋 Available endpoints:')
    console.log('  GET  /health                     - Health check')
    console.log('  POST /api/v1/services/register   - Register new service')
    console.log('  POST /api/v1/auth/token          - Get access token')
    console.log('  POST /api/v1/auth/refresh        - Refresh token')
    console.log('  POST /api/v1/files/upload        - Upload files')
    console.log('  GET  /api/v1/files/read          - Read file')
    console.log('  GET  /api/v1/files/list          - List files')
    console.log('  DEL  /api/v1/files/delete        - Delete file')
    console.log('  GET  /api/v1/services/status     - Service status')
    console.log('  GET  /api/v1/services/metrics    - Service metrics')
    console.log('')
    console.log('📖 API Documentation: docs/FILE_ROUTING_SERVICE_API.md')
    console.log('')
    
    return server
  } catch (error) {
    console.error('❌ Failed to start API server:', error)
    throw error
  }
}

/**
 * Stop the API server gracefully
 */
async function stopServer(server: FileRoutingApiServer): Promise<void> {
  console.log('🛑 Stopping FileRoutingService API Server...')
  
  try {
    await server.stop()
    console.log('✅ API Server stopped successfully!')
  } catch (error) {
    console.error('❌ Error stopping API server:', error)
    throw error
  }
}

// Handle graceful shutdown
process.on('SIGINT', async () => {
  console.log('\n🛑 Received SIGINT, shutting down gracefully...')
  process.exit(0)
})

process.on('SIGTERM', async () => {
  console.log('\n🛑 Received SIGTERM, shutting down gracefully...')
  process.exit(0)
})

// Export for programmatic usage
export { startServer, stopServer, defaultConfig }

// CLI usage
if (require.main === module) {
  startServer().catch(error => {
    console.error('Failed to start server:', error)
    process.exit(1)
  })
}
